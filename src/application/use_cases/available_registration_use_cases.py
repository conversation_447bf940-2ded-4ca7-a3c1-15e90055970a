from typing import List, Optional, Dict, Any
from src.application.interfaces.order_repository_interface import OrderRepositoryInterface
from src.application.interfaces.order_part_repository_interface import OrderPartRepositoryInterface
from src.application.interfaces.order_bundle_repository_interface import OrderBundleRepositoryInterface
from src.application.interfaces.order_craft_route_repository_interface import OrderCraftRouteRepositoryInterface
from src.application.interfaces.order_craft_route_instance_repository_interface import OrderCraftRouteInstanceRepositoryInterface
from src.application.dto.available_registration_dto import (
    AvailableRegistrationDTO, AvailableRegistrationQueryDTO, AvailablePartDTO,
    AvailableBundleDTO, AvailableCraftRouteDTO, RegistrationSummaryDTO
)
from src.domain.entities.order import OrderStatus


class AvailableRegistrationUseCases:
    """Use cases for getting available registration data."""
    
    def __init__(
        self,
        order_repository: OrderRepositoryInterface,
        order_part_repository: OrderPartRepositoryInterface,
        order_bundle_repository: OrderBundleRepositoryInterface,
        order_craft_route_repository: OrderCraftRouteRepositoryInterface,
        instance_repository: Order<PERSON>raftRouteInstanceRepositoryInterface
    ):
        self.order_repository = order_repository
        self.order_part_repository = order_part_repository
        self.order_bundle_repository = order_bundle_repository
        self.order_craft_route_repository = order_craft_route_repository
        self.instance_repository = instance_repository
    
    async def get_available_registration_data(
        self, 
        query: AvailableRegistrationQueryDTO,
        factory_id: int
    ) -> AvailableRegistrationDTO:
        """Get available registration data for an order."""
        
        try:
            # Get order information
            order = await self.order_repository.get_by_order_no(query.order_no)
            if not order:
                raise ValueError(f"Order {query.order_no} not found")
        except Exception as e:
            print(f"Error getting order {query.order_no}: {e}")
            raise
        
        # Only allow registration for orders that are in progress
        print(f"Order {query.order_no} status: {order.status}")
        if order.status not in [OrderStatus.IN_PROGRESS]:
            print(f"Order {query.order_no} is not IN_PROGRESS, returning empty data")
            return AvailableRegistrationDTO(
                order_no=query.order_no,
                order_status=order.status.value,
                total_quantity=order.total_amount,
                registered_quantity=0,
                available_quantity=0,
                craft_routes=[],
                order_parts=[]
            )
        
        try:
            # Get craft routes for the order
            craft_routes = await self.order_craft_route_repository.get_by_order_no(query.order_no)
            print(f"Found {len(craft_routes)} craft routes for order {query.order_no}")
        except Exception as e:
            print(f"Error getting craft routes for order {query.order_no}: {e}")
            raise
        
        # Filter by specific craft route if requested
        if query.craft_route_id:
            original_count = len(craft_routes)
            craft_routes = [cr for cr in craft_routes if cr.id == query.craft_route_id]
            print(f"Filtered craft routes by route_id {query.craft_route_id}: {original_count} -> {len(craft_routes)}")
        
        # Filter out completed routes if not including them
        if not query.include_completed_routes:
            original_count = len(craft_routes)
            craft_routes = [cr for cr in craft_routes if cr.status != "COMPLETED"]
            print(f"Filtered out completed routes: {original_count} -> {len(craft_routes)}")
        
        try:
            # Get registered data summary
            print(f"Getting registered data for factory_id={factory_id}, order_no={query.order_no}, craft_route_id={query.craft_route_id}")
            registered_summary = await self.instance_repository.get_registered_parts_and_bundles(
                factory_id, query.order_no, None, query.craft_route_id
            )
            print(f"Registered summary: {registered_summary}")
        except Exception as e:
            print(f"Error getting registered parts and bundles: {e}")
            raise
        registered_parts = registered_summary.get("parts", {})
        registered_bundles = registered_summary.get("bundles", {})
        
        try:
            # Get order parts and bundles
            order_parts = await self.order_part_repository.get_by_order_no_and_factory(query.order_no, factory_id)
            print(f"Found {len(order_parts)} order parts for order {query.order_no}")
        except Exception as e:
            print(f"Error getting order parts for order {query.order_no}: {e}")
            raise
        
        # Build available craft routes
        available_craft_routes = []
        try:
            for i, craft_route in enumerate(craft_routes):
                print(f"Processing craft route {i+1}/{len(craft_routes)}: {craft_route.id}")
                # Get registered quantity for this craft route
                route_summary = await self.instance_repository.get_registered_summary_by_craft_route(
                    factory_id, craft_route.id
                )
                registered_quantity = route_summary.get("total_registered_quantity", 0)
                print(f"Craft route {craft_route.id} registered quantity: {registered_quantity}")
                
                available_craft_route = AvailableCraftRouteDTO(
                    order_craft_route_id=craft_route.id,
                    craft_route_name=craft_route.name or f"Route {craft_route.id}",
                    skill_code=craft_route.skill_code,
                    skill_name=craft_route.skill.name if craft_route.skill else None,
                    route_status=craft_route.status,
                    total_quantity=order.total_amount,  # Use order's total amount
                    registered_quantity=registered_quantity,
                    available_quantity=max(0, order.total_amount - registered_quantity),
                    supports_order_level=True,
                    supports_part_level=len(order_parts) > 0,
                    supports_bundle_level=any(len(part.order_bundles) > 0 for part in order_parts)
                )
                available_craft_routes.append(available_craft_route)
        except Exception as e:
            print(f"Error processing craft routes: {e}")
            raise
        
        # Build available parts structure
        available_parts = []
        for part in order_parts:
            # Calculate registered quantity for this part
            part_registered_quantity = registered_parts.get(part.order_part_no, 0)
            part_available_quantity = max(0, part.total_quantity - part_registered_quantity)
            
            # Skip part if no quantity available and filtering by granularity
            if query.granularity_filter == "part" and part_available_quantity == 0:
                continue
            
            # Get bundles for this part
            available_bundles = []
            part_bundles = registered_bundles.get(part.order_part_no, {})
            
            for bundle in part.order_bundles:
                bundle_registered_quantity = part_bundles.get(bundle.order_bundle_no, 0)
                bundle_available_quantity = max(0, bundle.quantity - bundle_registered_quantity)
                
                # Skip bundle if no quantity available and filtering by granularity
                if query.granularity_filter == "bundle" and bundle_available_quantity == 0:
                    continue
                
                available_bundle = AvailableBundleDTO(
                    order_bundle_no=bundle.order_bundle_no,
                    total_quantity=bundle.quantity,
                    registered_quantity=bundle_registered_quantity,
                    available_quantity=bundle_available_quantity
                )
                available_bundles.append(available_bundle)
            
            # Only include part if it has available quantity or bundles
            if part_available_quantity > 0 or available_bundles:
                available_part = AvailablePartDTO(
                    order_part_no=part.order_part_no,
                    total_quantity=part.total_quantity,
                    registered_quantity=part_registered_quantity,
                    available_quantity=part_available_quantity,
                    order_bundles=available_bundles
                )
                available_parts.append(available_part)
        
        # Calculate total registered quantity for the order
        order_summary = await self.instance_repository.get_registered_summary_by_order(
            factory_id, query.order_no
        )
        total_registered_quantity = order_summary.get("total_registered_quantity", 0)
        
        return AvailableRegistrationDTO(
            order_no=query.order_no,
            order_status=order.status.value,
            total_quantity=order.total_amount,
            registered_quantity=total_registered_quantity,
            available_quantity=max(0, order.total_amount - total_registered_quantity),
            craft_routes=available_craft_routes,
            order_parts=available_parts
        )
    
    async def get_registration_summary(
        self, 
        order_no: str,
        factory_id: int
    ) -> RegistrationSummaryDTO:
        """Get registration summary for an order."""
        
        # Get order information
        order = await self.order_repository.get_by_order_no(order_no)
        if not order:
            raise ValueError(f"Order {order_no} not found")
        
        # Get craft routes
        craft_routes = await self.order_craft_route_repository.get_by_order_no(order_no)
        available_craft_routes = [cr for cr in craft_routes if cr.status != "COMPLETED"]
        
        # Get order parts and bundles
        order_parts = await self.order_part_repository.get_by_order_no(order_no)
        
        # Get registered data
        registered_summary = await self.instance_repository.get_registered_parts_and_bundles(
            factory_id, order_no, None
        )
        registered_parts = registered_summary.get("parts", {})
        registered_bundles = registered_summary.get("bundles", {})
        
        # Calculate available parts and bundles
        available_parts = []
        total_bundles = 0
        available_bundles = 0
        
        for part in order_parts:
            part_registered = registered_parts.get(part.order_part_no, 0)
            if part.total_quantity > part_registered:
                available_parts.append(part)
            
            # Count bundles
            for bundle in part.order_bundles:
                total_bundles += 1
                bundle_registered = registered_bundles.get(part.order_part_no, {}).get(bundle.order_bundle_no, 0)
                if bundle.quantity > bundle_registered:
                    available_bundles += 1
        
        # Calculate completion rate
        order_summary = await self.instance_repository.get_registered_summary_by_order(
            factory_id, order_no
        )
        total_registered_quantity = order_summary.get("total_registered_quantity", 0)
        completion_rate = total_registered_quantity / order.total_amount if order.total_amount > 0 else 0.0
        
        return RegistrationSummaryDTO(
            total_craft_routes=len(craft_routes),
            available_craft_routes=len(available_craft_routes),
            total_parts=len(order_parts),
            available_parts=len(available_parts),
            total_bundles=total_bundles,
            available_bundles=available_bundles,
            completion_rate=completion_rate
        )