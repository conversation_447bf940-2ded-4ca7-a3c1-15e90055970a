from fastapi import APIRouter
from .auth import router as auth_router
from .auth_phone import router as auth_phone_router
from .users import router as users_router
from .factory_management import router as factory_management_router
from .session import router as session_router
from .permissions import router as permissions_router
from .roles import router as roles_router
from .user_management import router as user_management_router
from .skills import router as skills_router
from .crafts import router as crafts_router
from .orders import router as orders_router
from .order_parts import router as order_parts_router
from .order_bundles import router as order_bundles_router
from .order_crafts import router as order_crafts_router
from .craft_instances import router as craft_instances_router
from .available_registration import router as available_registration_router

api_router = APIRouter()
api_router.include_router(auth_router)
api_router.include_router(auth_phone_router)
api_router.include_router(users_router)
api_router.include_router(factory_management_router)
api_router.include_router(session_router)
api_router.include_router(permissions_router)
api_router.include_router(roles_router)
api_router.include_router(user_management_router)
api_router.include_router(skills_router)
api_router.include_router(crafts_router)
api_router.include_router(orders_router)
api_router.include_router(order_parts_router)
api_router.include_router(order_bundles_router)
api_router.include_router(order_crafts_router)
api_router.include_router(craft_instances_router)
api_router.include_router(available_registration_router)