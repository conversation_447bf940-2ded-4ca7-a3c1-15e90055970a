from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from dependency_injector.wiring import Provide, inject

from src.infrastructure.containers import Container
from src.infrastructure.logging.logger import get_logger
from src.application.use_cases.available_registration_use_cases import AvailableRegistrationUseCases
from src.application.dto.available_registration_dto import (
    AvailableRegistrationDTO, AvailableRegistrationQueryDTO, RegistrationSummaryDTO
)
from src.presentation.api.v1.auth import get_current_active_user
from src.domain.entities.user import User
from src.presentation.api.v1.factory_management import get_current_factory_from_session

router = APIRouter(prefix="/available-registration", tags=["available-registration"])
logger = get_logger(__name__)


@router.get("/{order_no}", response_model=AvailableRegistrationDTO, operation_id="getAvailableRegistrationData")
@inject
async def get_available_registration_data(
    order_no: str,
    craft_route_id: Optional[int] = Query(None, description="筛选指定工艺路线ID"),
    include_completed_routes: bool = Query(False, description="是否包含已完成的路线"),
    granularity_filter: Optional[str] = Query(None, description="粒度过滤: order/part/bundle"),
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    available_registration_use_cases: AvailableRegistrationUseCases = Depends(Provide[Container.available_registration_use_cases])
):
    """Get available registration data for an order - returns parts/bundles that can still be registered."""
    logger.info(
        "Getting available registration data",
        user_id=current_user.id,
        factory_id=current_factory_id,
        order_no=order_no,
        craft_route_id=craft_route_id,
        include_completed_routes=include_completed_routes,
        granularity_filter=granularity_filter
    )
    
    # Also print for console debugging
    print(f"API call: order_no={order_no}, craft_route_id={craft_route_id}, factory_id={current_factory_id}, user_id={current_user.id}")
    print(f"Parameters: include_completed_routes={include_completed_routes}, granularity_filter={granularity_filter}")
    
    # Check permissions first (don't catch HTTPException)
    if not current_user.has_permission("craft_instances.read"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied: insufficient privileges to view available registration data"
        )

    try:
        query = AvailableRegistrationQueryDTO(
            order_no=order_no,
            craft_route_id=craft_route_id,
            include_completed_routes=include_completed_routes,
            granularity_filter=granularity_filter
        )
        
        result = await available_registration_use_cases.get_available_registration_data(query, current_factory_id)
        
        logger.info(
            "Available registration data retrieved successfully",
            user_id=current_user.id,
            order_no=order_no,
            total_available_quantity=result.available_quantity,
            available_craft_routes=len(result.craft_routes),
            available_parts=len(result.order_parts)
        )
        
        return result
        
    except ValueError as e:
        logger.warning(
            "Invalid request for available registration data",
            error=str(e),
            user_id=current_user.id,
            order_no=order_no,
            craft_route_id=craft_route_id,
            granularity_filter=granularity_filter
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(
            "Unexpected error in get_available_registration_data",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            order_no=order_no,
            craft_route_id=craft_route_id,
            granularity_filter=granularity_filter,
            factory_id=current_factory_id,
            include_completed_routes=include_completed_routes
        )
        # Print the full traceback for debugging
        import traceback
        print(f"Full traceback for error in get_available_registration_data:")
        traceback.print_exc()
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get available registration data: {str(e)}"
        )


@router.get("/{order_no}/summary", response_model=RegistrationSummaryDTO, operation_id="getRegistrationSummary")
@inject
async def get_registration_summary(
    order_no: str,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    available_registration_use_cases: AvailableRegistrationUseCases = Depends(Provide[Container.available_registration_use_cases])
):
    """Get registration summary for an order - overview of completion status."""
    logger.info(
        "Getting registration summary",
        user_id=current_user.id,
        factory_id=current_factory_id,
        order_no=order_no
    )
    
    # Check permissions first (don't catch HTTPException)
    if not current_user.has_permission("craft_instances.read"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied: insufficient privileges to view registration summary"
        )

    try:
        result = await available_registration_use_cases.get_registration_summary(order_no, current_factory_id)
        
        logger.info(
            "Registration summary retrieved successfully",
            user_id=current_user.id,
            order_no=order_no,
            completion_rate=result.completion_rate,
            available_craft_routes=result.available_craft_routes
        )
        
        return result
        
    except ValueError as e:
        logger.warning(
            "Invalid request for registration summary",
            error=str(e),
            user_id=current_user.id,
            order_no=order_no
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(
            "Unexpected error in get_registration_summary",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id,
            order_no=order_no
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get registration summary: {str(e)}"
        )