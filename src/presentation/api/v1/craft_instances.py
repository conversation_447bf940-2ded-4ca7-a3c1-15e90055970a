from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from dependency_injector.wiring import Provide, inject

from src.infrastructure.containers import Container
from src.infrastructure.logging.logger import get_logger
from src.application.use_cases.craft_instance_use_cases import CraftInstanceUseCases
from src.application.dto.craft_instance_dto import (
    CraftInstanceCreateDTO, CraftInstanceUpdateDTO, CraftInstanceResponseDTO,
    CraftInstanceVerificationDTO, CraftInstanceRejectionDTO, CraftInstanceSearchDTO,
    CraftInstanceListDTO, CraftInstanceOperationResultDTO, CraftInstanceStatisticsDTO,
    CraftInstanceBatchCreateDTO, CraftInstanceQRScanDTO, CompletionGranularityDTO, SettlementStatusDTO
)
from src.presentation.api.v1.auth import get_current_active_user
from src.domain.entities.user import User
from src.presentation.api.v1.factory_management import get_current_factory_from_session

router = APIRouter(prefix="/craft-instances", tags=["craft-instances"])
logger = get_logger(__name__)


@router.post("/", response_model=CraftInstanceOperationResultDTO)
@inject
async def create_craft_instance(
    data: CraftInstanceCreateDTO,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    craft_instance_use_cases: CraftInstanceUseCases = Depends(Provide[Container.craft_instance_use_cases])
):
    """Create a new craft instance - worker completion record."""
    logger.info(
        "Creating craft instance",
        user_id=current_user.id,
        factory_id=current_factory_id,
        order_no=data.order_no,
        worker_id=data.worker_user_id,
        quantity=data.completed_quantity
    )
    
    try:
        # Check permissions
        if not current_user.has_permission("craft_instances.create"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to create craft instances"
            )

        result = await craft_instance_use_cases.create_instance(data, current_factory_id)
        
        if result.success:
            logger.info(
                "Craft instance created successfully",
                user_id=current_user.id,
                instance_id=result.instance_id,
                order_no=data.order_no
            )
        else:
            logger.warning(
                "Failed to create craft instance",
                user_id=current_user.id,
                order_no=data.order_no,
                error=result.message
            )
        
        return result
        
    except Exception as e:
        logger.error(
            "Unexpected error in create_craft_instance",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create craft instance: {str(e)}"
        )


@router.post("/qr-scan", response_model=CraftInstanceOperationResultDTO)
@inject
async def qr_scan_register(
    data: CraftInstanceQRScanDTO,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    craft_instance_use_cases: CraftInstanceUseCases = Depends(Provide[Container.craft_instance_use_cases])
):
    """Register completion via QR code scanning."""
    logger.info(
        "QR scan registration",
        user_id=current_user.id,
        factory_id=current_factory_id,
        qr_code=data.qr_code_content,
        quantity=data.completed_quantity
    )
    
    try:
        # Check permissions
        if not current_user.has_permission("craft_instances.create"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to register completions"
            )

        result = await craft_instance_use_cases.qr_scan_register(data, current_user.id, current_factory_id)
        
        if result.success:
            logger.info(
                "QR scan registration successful",
                user_id=current_user.id,
                instance_id=result.instance_id,
                qr_code=data.qr_code_content
            )
        else:
            logger.warning(
                "QR scan registration failed",
                user_id=current_user.id,
                qr_code=data.qr_code_content,
                error=result.message
            )
        
        return result
        
    except Exception as e:
        logger.error(
            "Unexpected error in qr_scan_register",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to register via QR scan: {str(e)}"
        )


@router.get("/search", response_model=CraftInstanceListDTO)
@inject
async def search_craft_instances(
    order_no: Optional[str] = Query(None, description="订单号"),
    worker_user_id: Optional[int] = Query(None, description="工人ID"),
    completion_granularity: Optional[CompletionGranularityDTO] = Query(None, description="完成粒度"),
    status: Optional[str] = Query(None, description="状态"),
    settlement_status: Optional[SettlementStatusDTO] = Query(None, description="结算状态"),
    quality_level: Optional[str] = Query(None, description="质量等级"),
    limit: int = Query(50, ge=1, le=1000, description="每页数量"),
    offset: int = Query(0, ge=0, description="偏移量"),
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    craft_instance_use_cases: CraftInstanceUseCases = Depends(Provide[Container.craft_instance_use_cases])
):
    """Search craft instances with filters."""
    logger.info(
        "Searching craft instances",
        user_id=current_user.id,
        factory_id=current_factory_id,
        order_no=order_no,
        worker_id=worker_user_id
    )
    
    try:
        # Check permissions
        if not current_user.has_permission("craft_instances.read"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view craft instances"
            )

        search_data = CraftInstanceSearchDTO(
            order_no=order_no,
            worker_user_id=worker_user_id,
            completion_granularity=completion_granularity,
            status=status,
            settlement_status=settlement_status,
            quality_level=quality_level
        )
        
        result = await craft_instance_use_cases.search_instances(search_data, limit, offset)
        
        logger.info(
            "Craft instances search completed",
            user_id=current_user.id,
            total_found=result.total,
            returned_count=len(result.instances)
        )
        
        return result
        
    except Exception as e:
        logger.error(
            "Unexpected error in search_craft_instances",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to search craft instances: {str(e)}"
        )


@router.get("/{instance_id}", response_model=CraftInstanceResponseDTO)
@inject
async def get_craft_instance(
    instance_id: int,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    craft_instance_use_cases: CraftInstanceUseCases = Depends(Provide[Container.craft_instance_use_cases])
):
    """Get craft instance by ID."""
    logger.info(
        "Getting craft instance",
        user_id=current_user.id,
        factory_id=current_factory_id,
        instance_id=instance_id
    )
    
    try:
        # Check permissions
        if not current_user.has_permission("craft_instances.read"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view craft instances"
            )

        instance = await craft_instance_use_cases.get_instance_by_id(instance_id)
        
        if not instance:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Craft instance {instance_id} not found"
            )
        
        logger.info(
            "Craft instance retrieved successfully",
            user_id=current_user.id,
            instance_id=instance_id
        )
        
        return instance
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Unexpected error in get_craft_instance",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get craft instance: {str(e)}"
        )


@router.post("/{instance_id}/verify", response_model=CraftInstanceOperationResultDTO)
@inject
async def verify_craft_instance(
    instance_id: int,
    data: CraftInstanceVerificationDTO,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    craft_instance_use_cases: CraftInstanceUseCases = Depends(Provide[Container.craft_instance_use_cases])
):
    """Verify a craft instance."""
    logger.info(
        "Verifying craft instance",
        user_id=current_user.id,
        factory_id=current_factory_id,
        instance_id=instance_id,
        verified_by=data.verified_by_user_id
    )
    
    try:
        # Check permissions
        if not current_user.has_permission("craft_instances.verify"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to verify craft instances"
            )

        result = await craft_instance_use_cases.verify_instance(instance_id, data)
        
        if result.success:
            logger.info(
                "Craft instance verified successfully",
                user_id=current_user.id,
                instance_id=instance_id
            )
        else:
            logger.warning(
                "Failed to verify craft instance",
                user_id=current_user.id,
                instance_id=instance_id,
                error=result.message
            )
        
        return result
        
    except Exception as e:
        logger.error(
            "Unexpected error in verify_craft_instance",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to verify craft instance: {str(e)}"
        )


@router.post("/{instance_id}/reject", response_model=CraftInstanceOperationResultDTO)
@inject
async def reject_craft_instance(
    instance_id: int,
    data: CraftInstanceRejectionDTO,
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    craft_instance_use_cases: CraftInstanceUseCases = Depends(Provide[Container.craft_instance_use_cases])
):
    """Reject a craft instance."""
    logger.info(
        "Rejecting craft instance",
        user_id=current_user.id,
        factory_id=current_factory_id,
        instance_id=instance_id,
        rejected_by=data.rejected_by_user_id
    )
    
    try:
        # Check permissions
        if not current_user.has_permission("craft_instances.verify"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to reject craft instances"
            )

        result = await craft_instance_use_cases.reject_instance(instance_id, data)
        
        if result.success:
            logger.info(
                "Craft instance rejected successfully",
                user_id=current_user.id,
                instance_id=instance_id
            )
        else:
            logger.warning(
                "Failed to reject craft instance",
                user_id=current_user.id,
                instance_id=instance_id,
                error=result.message
            )
        
        return result
        
    except Exception as e:
        logger.error(
            "Unexpected error in reject_craft_instance",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to reject craft instance: {str(e)}"
        )


@router.get("/statistics/overview", response_model=CraftInstanceStatisticsDTO)
@inject
async def get_craft_instance_statistics(
    order_no: Optional[str] = Query(None, description="订单号"),
    worker_user_id: Optional[int] = Query(None, description="工人ID"),
    current_user: User = Depends(get_current_active_user),
    current_factory_id: int = Depends(get_current_factory_from_session),
    craft_instance_use_cases: CraftInstanceUseCases = Depends(Provide[Container.craft_instance_use_cases])
):
    """Get craft instance statistics."""
    logger.info(
        "Getting craft instance statistics",
        user_id=current_user.id,
        factory_id=current_factory_id,
        order_no=order_no,
        worker_id=worker_user_id
    )
    
    try:
        # Check permissions
        if not current_user.has_permission("craft_instances.read"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Permission denied: insufficient privileges to view statistics"
            )

        stats = await craft_instance_use_cases.get_statistics(
            order_no=order_no,
            worker_user_id=worker_user_id
        )
        
        logger.info(
            "Statistics retrieved successfully",
            user_id=current_user.id,
            total_instances=stats.total_instances
        )
        
        return stats
        
    except Exception as e:
        logger.error(
            "Unexpected error in get_craft_instance_statistics",
            error=str(e),
            error_type=type(e).__name__,
            user_id=current_user.id
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get statistics: {str(e)}"
        )