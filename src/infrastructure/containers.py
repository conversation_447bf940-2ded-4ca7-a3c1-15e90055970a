from dependency_injector import containers, providers
from dependency_injector.wiring import Provide, inject

from config import settings
from src.infrastructure.database.database import Database
# Import all entities to ensure SQLAlchemy mappings are loaded
from src.domain.entities import all as entities  # noqa
from src.infrastructure.external_services.redis_service import RedisService
from src.infrastructure.external_services.sms_service import create_sms_service
from src.infrastructure.external_services.captcha_service import create_captcha_service
from src.infrastructure.repositories.user_repository import UserRepository
from src.infrastructure.repositories.factory_repository import FactoryRepository
from src.infrastructure.repositories.department_repository import DepartmentRepository
from src.infrastructure.repositories.validation_code_repository import ValidationCodeRepository
from src.infrastructure.repositories.user_factory_repository import UserFactoryRepository
from src.infrastructure.repositories.permission_repository import PermissionRepository
from src.infrastructure.repositories.role_repository import RoleRepository
from src.infrastructure.repositories.skill_repository import SkillRepository
from src.infrastructure.repositories.user_factory_skill_repository import UserFactorySkillRepository
from src.infrastructure.repositories.craft_repository import CraftRepository
from src.infrastructure.repositories.order_craft_repository import OrderCraftRepository
from src.infrastructure.repositories.order_craft_route_repository import OrderCraftRouteRepository
from src.infrastructure.repositories.craft_route_repository import CraftRouteRepository
from src.infrastructure.repositories.order_repository import OrderRepository
from src.infrastructure.repositories.order_line_repository import OrderLineRepository
from src.infrastructure.repositories.order_part_repository import OrderPartRepository
from src.infrastructure.repositories.order_bundle_repository import OrderBundleRepository
from src.infrastructure.repositories.order_craft_route_instance_repository import OrderCraftRouteInstanceRepository
from src.application.interfaces.user_repository_interface import UserRepositoryInterface
from src.application.interfaces.factory_repository_interface import FactoryRepositoryInterface
from src.application.interfaces.department_repository_interface import DepartmentRepositoryInterface
from src.application.interfaces.validation_code_repository_interface import ValidationCodeRepositoryInterface
from src.application.interfaces.user_factory_repository_interface import UserFactoryRepositoryInterface
from src.application.interfaces.permission_repository_interface import PermissionRepositoryInterface
from src.application.interfaces.role_repository_interface import RoleRepositoryInterface
from src.application.interfaces.skill_repository_interface import SkillRepositoryInterface
from src.application.interfaces.user_factory_skill_repository_interface import UserFactorySkillRepositoryInterface
from src.application.interfaces.craft_repository_interface import CraftRepositoryInterface
from src.application.interfaces.craft_route_repository_interface import CraftRouteRepositoryInterface
from src.application.interfaces.order_craft_repository_interface import OrderCraftRepositoryInterface
from src.application.interfaces.order_craft_route_repository_interface import OrderCraftRouteRepositoryInterface
from src.application.interfaces.order_repository_interface import OrderRepositoryInterface
from src.application.interfaces.order_line_repository_interface import OrderLineRepositoryInterface
from src.application.interfaces.order_part_repository_interface import OrderPartRepositoryInterface
from src.application.interfaces.order_bundle_repository_interface import OrderBundleRepositoryInterface
from src.application.interfaces.order_craft_route_instance_repository_interface import OrderCraftRouteInstanceRepositoryInterface
from src.application.use_cases.user_use_cases import UserUseCases
from src.application.use_cases.auth_use_cases import AuthUseCases
from src.application.use_cases.factory_use_cases import FactoryUseCases
from src.application.use_cases.session_use_cases import SessionUseCases
from src.application.use_cases.permission_use_cases import PermissionUseCases
from src.application.use_cases.role_use_cases import RoleUseCases
from src.application.use_cases.user_management_use_cases import UserManagementUseCases
from src.application.use_cases.skill_use_cases import SkillUseCases
from src.application.use_cases.craft_use_cases import CraftUseCases
from src.application.use_cases.department_use_cases import DepartmentUseCases
from src.application.use_cases.order_use_cases import OrderUseCases
from src.application.use_cases.order_part_use_cases import OrderPartUseCases
from src.application.use_cases.order_bundle_use_cases import OrderBundleUseCases
from src.application.use_cases.order_craft_use_cases import OrderCraftUseCases
from src.application.use_cases.available_registration_use_cases import AvailableRegistrationUseCases
from src.application.use_cases.craft_instance_use_cases import CraftInstanceUseCases
from src.infrastructure.external_services.session_service import create_session_service
from src.domain.services.validation_code_service import ValidationCodeService, ValidationCodeGenerator


class Container(containers.DeclarativeContainer):
    """Dependency injection container."""
    
    # Configuration
    config = providers.Configuration()
    
    # Database
    db = providers.Singleton(
        Database,
        db_url=settings.database.url,
        echo=settings.database.echo
    )
    
    # Redis
    redis = providers.Singleton(
        RedisService,
        redis_url=settings.redis.url
    )
    
    # External Services
    sms_service = providers.Singleton(create_sms_service)
    captcha_service = providers.Singleton(create_captcha_service)
    session_service = providers.Singleton(create_session_service, redis_service=redis)
    
    # Domain Services
    validation_code_generator = providers.Factory(ValidationCodeGenerator)
    validation_code_service = providers.Factory(
        ValidationCodeService,
        generator=validation_code_generator
    )
    
    # Repositories
    user_repository = providers.Factory(
        UserRepository,
        session_factory=db.provided.session_factory
    )
    
    factory_repository = providers.Factory(
        FactoryRepository,
        session_factory=db.provided.session_factory
    )
    
    department_repository = providers.Factory(
        DepartmentRepository,
        session_factory=db.provided.session_factory
    )
    
    validation_code_repository = providers.Factory(
        ValidationCodeRepository,
        session_factory=db.provided.session_factory
    )
    
    user_factory_repository = providers.Factory(
        UserFactoryRepository,
        session_factory=db.provided.session_factory
    )
    
    permission_repository = providers.Factory(
        PermissionRepository,
        session_factory=db.provided.session_factory
    )
    
    role_repository = providers.Factory(
        RoleRepository,
        session_factory=db.provided.session_factory
    )
    
    skill_repository = providers.Factory(
        SkillRepository,
        session_factory=db.provided.session_factory
    )
    
    user_factory_skill_repository = providers.Factory(
        UserFactorySkillRepository,
        session_factory=db.provided.session_factory
    )
    
    craft_repository = providers.Factory(
        CraftRepository,
        session_factory=db.provided.session_factory
    )
    
    craft_route_repository = providers.Factory(
        CraftRouteRepository,
        session_factory=db.provided.session_factory
    )
    
    order_craft_repository = providers.Factory(
        OrderCraftRepository,
        session_factory=db.provided.session_factory
    )
    
    order_craft_route_repository = providers.Factory(
        OrderCraftRouteRepository,
        session_factory=db.provided.session_factory
    )
    
    order_repository = providers.Factory(
        OrderRepository,
        session_factory=db.provided.session_factory
    )
    
    order_line_repository = providers.Factory(
        OrderLineRepository,
        session_factory=db.provided.session_factory
    )
    
    order_part_repository = providers.Factory(
        OrderPartRepository,
        session_factory=db.provided.session_factory
    )
    
    order_bundle_repository = providers.Factory(
        OrderBundleRepository,
        session_factory=db.provided.session_factory
    )
    
    order_craft_route_instance_repository = providers.Factory(
        OrderCraftRouteInstanceRepository,
        session_factory=db.provided.session_factory
    )
    
    # Use Cases
    user_use_cases = providers.Factory(
        UserUseCases,
        user_repository=user_repository
    )
    
    auth_use_cases = providers.Factory(
        AuthUseCases,
        user_repository=user_repository,
        validation_code_repository=validation_code_repository,
        validation_code_service=validation_code_service,
        user_use_cases=user_use_cases
    )
    
    factory_use_cases = providers.Factory(
        FactoryUseCases,
        user_repository=user_repository,
        factory_repository=factory_repository,
        user_factory_repository=user_factory_repository
    )
    
    session_use_cases = providers.Factory(
        SessionUseCases,
        session_service=session_service,
        user_factory_repository=user_factory_repository
    )
    
    permission_use_cases = providers.Factory(
        PermissionUseCases,
        permission_repository=permission_repository
    )
    
    role_use_cases = providers.Factory(
        RoleUseCases,
        role_repository=role_repository,
        permission_repository=permission_repository
    )
    
    user_management_use_cases = providers.Factory(
        UserManagementUseCases,
        user_repository=user_repository,
        role_repository=role_repository,
        factory_repository=factory_repository,
        user_factory_repository=user_factory_repository,
        skill_repository=skill_repository,
        user_factory_skill_repository=user_factory_skill_repository
    )
    
    skill_use_cases = providers.Factory(
        SkillUseCases,
        skill_repository=skill_repository,
        user_factory_skill_repository=user_factory_skill_repository,
        user_factory_repository=user_factory_repository
    )
    
    craft_use_cases = providers.Factory(
        CraftUseCases,
        craft_repository=craft_repository,
        craft_route_repository=craft_route_repository,
        skill_repository=skill_repository
    )

    department_use_cases = providers.Factory(
        DepartmentUseCases,
        department_repository=department_repository
    )

    order_part_use_cases = providers.Factory(
        OrderPartUseCases,
        order_part_repository=order_part_repository,
        order_bundle_repository=order_bundle_repository,
        order_repository=order_repository,
        user_repository=user_repository
    )
    
    order_use_cases = providers.Factory(
        OrderUseCases,
        order_repository=order_repository,
        order_line_repository=order_line_repository,
        user_repository=user_repository,
        craft_repository=craft_repository,
        craft_route_repository=craft_route_repository,
        order_part_use_cases=order_part_use_cases
    )
    
    order_bundle_use_cases = providers.Factory(
        OrderBundleUseCases,
        order_bundle_repository=order_bundle_repository,
        order_part_repository=order_part_repository,
        order_repository=order_repository,
        user_repository=user_repository
    )
    
    order_craft_use_cases = providers.Factory(
        OrderCraftUseCases,
        order_craft_repository=order_craft_repository,
        order_craft_route_repository=order_craft_route_repository,
        order_repository=order_repository,
        craft_repository=craft_repository,
        skill_repository=skill_repository,
        user_repository=user_repository
    )
    
    available_registration_use_cases = providers.Factory(
        AvailableRegistrationUseCases,
        order_repository=order_repository,
        order_part_repository=order_part_repository,
        order_bundle_repository=order_bundle_repository,
        order_craft_route_repository=order_craft_route_repository,
        instance_repository=order_craft_route_instance_repository
    )
    
    craft_instance_use_cases = providers.Factory(
        CraftInstanceUseCases,
        instance_repository=order_craft_route_instance_repository,
        route_repository=order_craft_route_repository,
        user_repository=user_repository
    )


container = Container()
container.config.from_dict(settings.model_dump())