{"openapi": "3.1.0", "info": {"title": "Production Ticket System", "version": "1.0.0"}, "paths": {"/api/v1/auth/token": {"post": {"tags": ["authentication"], "summary": "<PERSON><PERSON>", "description": "Login and get access token with session context.", "operationId": "login_api_v1_auth_token_post", "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/Body_login_api_v1_auth_token_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/src__application__dto__user_dto__TokenResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/me": {"get": {"tags": ["authentication"], "summary": "Read Users Me", "description": "Get current user information.", "operationId": "read_users_me_api_v1_auth_me_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponseDTO"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/auth/generate-image-code": {"post": {"tags": ["phone-authentication"], "summary": "Generate Image Code", "description": "Generate image validation code.", "operationId": "generate_image_code_api_v1_auth_generate_image_code_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateImageCodeDTO"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ImageCodeResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/send-sms-code": {"post": {"tags": ["phone-authentication"], "summary": "Send Sms Code", "description": "Send SMS validation code.", "operationId": "send_sms_code_api_v1_auth_send_sms_code_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendSmsCodeDTO"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendSmsCodeResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/login-phone-password": {"post": {"tags": ["phone-authentication"], "summary": "Login With Phone Password", "description": "Login with phone + password + image validation code.", "operationId": "login_with_phone_password_api_v1_auth_login_phone_password_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PhonePasswordLoginDTO"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/src__application__dto__auth_dto__TokenResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/login-phone-sms": {"post": {"tags": ["phone-authentication"], "summary": "Login With Phone Sms", "description": "Login with phone + SMS code + image validation code.", "operationId": "login_with_phone_sms_api_v1_auth_login_phone_sms_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PhoneSmsLoginDTO"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/src__application__dto__auth_dto__TokenResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/cleanup-expired-codes": {"post": {"tags": ["phone-authentication"], "summary": "Cleanup Expired Codes", "description": "Clean up expired validation codes (admin endpoint).", "operationId": "cleanup_expired_codes_api_v1_auth_cleanup_expired_codes_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/users/": {"post": {"tags": ["users"], "summary": "Create User", "description": "Create a new user.", "operationId": "create_user_api_v1_users__post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreateDTO"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["users"], "summary": "Get Users", "description": "Get all users.", "operationId": "get_users_api_v1_users__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserResponseDTO"}, "title": "Response Get Users Api V1 Users  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/users/{user_id}": {"get": {"tags": ["users"], "summary": "Get User", "description": "Get user by ID.", "operationId": "get_user_api_v1_users__user_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["users"], "summary": "Update User", "description": "Update user.", "operationId": "update_user_api_v1_users__user_id__put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdateDTO"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["users"], "summary": "Delete User", "description": "Delete user.", "operationId": "delete_user_api_v1_users__user_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/factory-management/join-request": {"post": {"tags": ["factory-management"], "summary": "Request To Join Factory", "description": "Request to join a factory.", "operationId": "request_to_join_factory_api_v1_factory_management_join_request_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FactoryJoinRequestDTO"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserFactoryResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/factory-management/approve-request": {"post": {"tags": ["factory-management"], "summary": "Approve Or Reject Request", "description": "Approve or reject a factory join request (managers only).", "operationId": "approve_or_reject_request_api_v1_factory_management_approve_request_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FactoryJoinApprovalDTO"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserFactoryResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/factory-management/my-factories": {"get": {"tags": ["factory-management"], "summary": "Get My Factories", "description": "Get current user's factory relationships.", "operationId": "get_my_factories_api_v1_factory_management_my_factories_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MyFactoriesDTO"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/factory-management/pending-requests": {"get": {"tags": ["factory-management"], "summary": "Get Pending Requests", "description": "Get pending join requests for current factory (managers only).", "operationId": "get_pending_requests_api_v1_factory_management_pending_requests_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/UserFactoryResponseDTO"}, "type": "array", "title": "Response Get Pending Requests Api V1 Factory Management Pending Requests Get"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/factory-management/members": {"get": {"tags": ["factory-management"], "summary": "Get Factory Members", "description": "Get all members of current factory.", "operationId": "get_factory_members_api_v1_factory_management_members_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/UserFactoryResponseDTO"}, "type": "array", "title": "Response Get Factory Members Api V1 Factory Management Members Get"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/factory-management/user/{user_id}/role": {"put": {"tags": ["factory-management"], "summary": "Update User Role", "description": "Update user's role in current factory (managers only).", "operationId": "update_user_role_api_v1_factory_management_user__user_id__role_put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}, {"name": "new_role", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/UserFactoryRole"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserFactoryResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/factory-management/user/{user_id}/suspend": {"post": {"tags": ["factory-management"], "summary": "Suspend User", "description": "Suspend user from current factory (managers only).", "operationId": "suspend_user_api_v1_factory_management_user__user_id__suspend_post", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}, {"name": "reason", "in": "query", "required": false, "schema": {"type": "string", "title": "Reason"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserFactoryResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/factory-management/resign": {"post": {"tags": ["factory-management"], "summary": "Resign From Factory", "description": "Resign from current factory.", "operationId": "resign_from_factory_api_v1_factory_management_resign_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserFactoryResponseDTO"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/session/status": {"get": {"tags": ["session"], "summary": "Get Session Status", "description": "Get current session status and factory context.", "operationId": "get_session_status_api_v1_session_status_get", "parameters": [{"name": "x-session-id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Session-Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SessionStatusDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/session/available-factories": {"get": {"tags": ["session"], "summary": "Get Available Factories", "description": "Get list of factories user can switch to.", "operationId": "get_available_factories_api_v1_session_available_factories_get", "parameters": [{"name": "x-session-id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Session-Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AvailableFactoriesDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/session/switch-factory": {"post": {"tags": ["session"], "summary": "Switch Factory Context", "description": "Switch user's factory context.", "operationId": "switch_factory_context_api_v1_session_switch_factory_post", "parameters": [{"name": "x-session-id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Session-Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SwitchFactoryDTO"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserSessionDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/session/refresh-context": {"post": {"tags": ["session"], "summary": "Refresh Factory Context", "description": "Refresh factory context with latest data.", "operationId": "refresh_factory_context_api_v1_session_refresh_context_post", "parameters": [{"name": "x-session-id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Session-Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserSessionDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/session/extend": {"post": {"tags": ["session"], "summary": "Extend Session", "description": "Extend session expiration.", "operationId": "extend_session_api_v1_session_extend_post", "parameters": [{"name": "x-session-id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Session-Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/session/logout": {"delete": {"tags": ["session"], "summary": "Logout", "description": "Logout and destroy session.", "operationId": "logout_api_v1_session_logout_delete", "parameters": [{"name": "x-session-id", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "X-Session-Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/permissions/tree": {"get": {"tags": ["permissions"], "summary": "Get Permission Tree", "description": "Get hierarchical permission tree.", "operationId": "get_permission_tree_api_v1_permissions_tree_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/PermissionTreeDTO"}, "type": "array", "title": "Response Get Permission Tree Api V1 Permissions Tree Get"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/permissions/list": {"get": {"tags": ["permissions"], "summary": "Get All Permissions", "description": "Get all permissions as a flat list.", "operationId": "get_all_permissions_api_v1_permissions_list_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PermissionListDTO"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/roles/": {"get": {"tags": ["roles"], "summary": "Get All Roles", "description": "Get all roles.", "operationId": "get_all_roles_api_v1_roles__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleListDTO"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}, "post": {"tags": ["roles"], "summary": "Create Role", "description": "Create a new role.", "operationId": "create_role_api_v1_roles__post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleCreateDTO"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/roles/active": {"get": {"tags": ["roles"], "summary": "Get Active Roles", "description": "Get all active roles.", "operationId": "get_active_roles_api_v1_roles_active_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleListDTO"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/roles/summary": {"get": {"tags": ["roles"], "summary": "Get Roles Summary", "description": "Get summary of all roles without full permission details.", "operationId": "get_roles_summary_api_v1_roles_summary_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/RoleSummaryDTO"}, "type": "array", "title": "Response Get Roles Summary Api V1 Roles Summary Get"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/roles/{role_id}": {"get": {"tags": ["roles"], "summary": "Get Role By Id", "description": "Get role by <PERSON>.", "operationId": "get_role_by_id_api_v1_roles__role_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "role_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Role Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["roles"], "summary": "Update Role", "description": "Update role.", "operationId": "update_role_api_v1_roles__role_id__put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "role_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Role Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleUpdateDTO"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["roles"], "summary": "Delete Role", "description": "Delete role.", "operationId": "delete_role_api_v1_roles__role_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "role_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Role Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/roles/{role_id}/permissions": {"post": {"tags": ["roles"], "summary": "Assign Permissions To Role", "description": "Assign permissions to role.", "operationId": "assign_permissions_to_role_api_v1_roles__role_id__permissions_post", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "role_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Role Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RolePermissionAssignDTO"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["roles"], "summary": "Remove Permissions From Role", "description": "Remove permissions from role.", "operationId": "remove_permissions_from_role_api_v1_roles__role_id__permissions_delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "role_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Role Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RolePermissionRemoveDTO"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RoleResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["roles"], "summary": "Get Role Permissions", "description": "Get permissions assigned to a role.", "operationId": "get_role_permissions_api_v1_roles__role_id__permissions_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "role_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Role Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/PermissionResponseDTO"}, "title": "Response Get Role Permissions Api V1 Roles  Role Id  Permissions Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/user-management/user-list": {"get": {"tags": ["user-management"], "summary": "Get Factory User List", "description": "Get list of all users in the current factory.", "operationId": "get_factory_user_list_api_v1_user_management_user_list_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FactoryUserListDTO"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/user-management/available-users": {"get": {"tags": ["user-management"], "summary": "Get Available Users", "description": "Get list of users available to add to the factory.", "operationId": "get_available_users_api_v1_user_management_available_users_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "search_term", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Search by username, email, or full name", "title": "Search Term"}, "description": "Search by username, email, or full name"}, {"name": "role_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "Filter by system role", "title": "Role Id"}, "description": "Filter by system role"}, {"name": "is_active", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "Filter by active status", "title": "Is Active"}, "description": "Filter by active status"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AvailableUsersDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/user-management/add-users": {"post": {"tags": ["user-management"], "summary": "Add Users To Factory", "description": "Add multiple users to the current factory.", "operationId": "add_users_to_factory_api_v1_user_management_add_users_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddUsersToFactoryDTO"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/UserOperationResultDTO"}, "type": "array", "title": "Response Add Users To Factory Api V1 User Management Add Users Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/user-management/bind-roles": {"post": {"tags": ["user-management"], "summary": "Bind User Role", "description": "Bind system role to a user.", "operationId": "bind_user_role_api_v1_user_management_bind_roles_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BindUserRoleDTO"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserOperationResultDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/user-management/suspend": {"post": {"tags": ["user-management"], "summary": "Suspend User In Factory", "description": "Suspend a user in the current factory.", "operationId": "suspend_user_in_factory_api_v1_user_management_suspend_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuspendUserDTO"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserOperationResultDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/user-management/remove-user": {"delete": {"tags": ["user-management"], "summary": "Remove User From Factory", "description": "Remove a user from the current factory.", "operationId": "remove_user_from_factory_api_v1_user_management_remove_user_delete", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RemoveUserFromFactoryDTO"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserOperationResultDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/user-management/create-user": {"post": {"tags": ["user-management"], "summary": "Create User With Factory", "description": "Create new user or add existing user to factory with skills and complete information.", "operationId": "create_user_with_factory_api_v1_user_management_create_user_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserWithFactoryDTO"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddUserWithFactoryResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/skills/": {"post": {"tags": ["skills"], "summary": "Create Skill", "description": "Create a new skill.", "operationId": "create_skill_api_v1_skills__post", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SkillCreateDTO"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SkillResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["skills"], "summary": "Get All Skills", "description": "Get all skills with optional filters.", "operationId": "get_all_skills_api_v1_skills__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "search_term", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Search by code, name, or description", "title": "Search Term"}, "description": "Search by code, name, or description"}, {"name": "category", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by category", "title": "Category"}, "description": "Filter by category"}, {"name": "is_active", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "Filter by active status", "title": "Is Active"}, "description": "Filter by active status"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SkillListDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/skills/active": {"get": {"tags": ["skills"], "summary": "Get Active Skills", "description": "Get all active skills.", "operationId": "get_active_skills_api_v1_skills_active_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SkillListDTO"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/skills/{skill_id}": {"get": {"tags": ["skills"], "summary": "Get Skill By Id", "description": "Get skill by ID.", "operationId": "get_skill_by_id_api_v1_skills__skill_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "skill_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Skill Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SkillResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["skills"], "summary": "Update Skill", "description": "Update skill.", "operationId": "update_skill_api_v1_skills__skill_id__put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "skill_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Skill Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SkillUpdateDTO"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SkillResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["skills"], "summary": "Delete Skill", "description": "Delete skill.", "operationId": "delete_skill_api_v1_skills__skill_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "skill_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Skill Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/skills/user/{user_id}/skills": {"get": {"tags": ["skills"], "summary": "Get User Skills", "description": "Get user's skills in current factory.", "operationId": "get_user_skills_api_v1_skills_user__user_id__skills_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserSkillsDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/skills/assign": {"post": {"tags": ["skills"], "summary": "Assign Skills To User", "description": "Assign skills to user in current factory.", "operationId": "assign_skills_to_user_api_v1_skills_assign_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssignSkillsDTO"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/SkillOperationResultDTO"}, "type": "array", "title": "Response Assign Skills To User Api V1 Skills Assign Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/skills/modify-proficiency": {"put": {"tags": ["skills"], "summary": "Modify Skill Proficiency", "description": "Modify user's skill proficiency level.", "operationId": "modify_skill_proficiency_api_v1_skills_modify_proficiency_put", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSkillProficiencyDTO"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SkillOperationResultDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/skills/certify": {"post": {"tags": ["skills"], "summary": "Certify User Skill", "description": "Certify user in skill.", "operationId": "certify_user_skill_api_v1_skills_certify_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CertifySkillDTO"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SkillOperationResultDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/skills/remove": {"delete": {"tags": ["skills"], "summary": "Remove User <PERSON>ll", "description": "Remove skill from user.", "operationId": "remove_user_skill_api_v1_skills_remove_delete", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RemoveSkillDTO"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SkillOperationResultDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/crafts/": {"post": {"tags": ["crafts"], "summary": "Create Craft", "description": "Create a new craft (admin only).", "operationId": "create_craft_api_v1_crafts__post", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CraftCreateDTO"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CraftResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["crafts"], "summary": "Get All Crafts", "description": "Get all crafts with optional filtering.", "operationId": "get_all_crafts_api_v1_crafts__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "search_term", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Search by code, name, or description", "title": "Search Term"}, "description": "Search by code, name, or description"}, {"name": "enabled", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "Filter by enabled status", "title": "Enabled"}, "description": "Filter by enabled status"}, {"name": "min_priority", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer", "minimum": 0}, {"type": "null"}], "description": "Minimum priority", "title": "Min Priority"}, "description": "Minimum priority"}, {"name": "max_priority", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer", "minimum": 0}, {"type": "null"}], "description": "Maximum priority", "title": "Max Priority"}, "description": "Maximum priority"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CraftListDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/crafts/enabled": {"get": {"tags": ["crafts"], "summary": "Get Enabled Crafts", "description": "Get all enabled crafts.", "operationId": "get_enabled_crafts_api_v1_crafts_enabled_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CraftListDTO"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/crafts/{craft_id}": {"get": {"tags": ["crafts"], "summary": "Get Craft By Id", "description": "Get craft by ID.", "operationId": "get_craft_by_id_api_v1_crafts__craft_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "craft_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Craft Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CraftResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["crafts"], "summary": "Update Craft", "description": "Update craft (admin only).", "operationId": "update_craft_api_v1_crafts__craft_id__put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "craft_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Craft Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CraftUpdateDTO"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CraftResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["crafts"], "summary": "Delete Craft", "description": "Delete craft (admin only).", "operationId": "delete_craft_api_v1_crafts__craft_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "craft_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Craft Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/crafts/code/{craft_code}": {"get": {"tags": ["crafts"], "summary": "Get Craft By Code", "description": "Get craft by code with its routes.", "operationId": "get_craft_by_code_api_v1_crafts_code__craft_code__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "craft_code", "in": "path", "required": true, "schema": {"type": "string", "title": "Craft Code"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CraftWithRoutesDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/crafts/routes": {"post": {"tags": ["crafts"], "summary": "Create Craft Route", "description": "Create a new craft route.", "operationId": "create_craft_route_api_v1_crafts_routes_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CraftRouteCreateDTO"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CraftRouteResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/crafts/routes/bulk": {"post": {"tags": ["crafts"], "summary": "Create Bulk Craft Routes", "description": "Create multiple craft routes at once.", "operationId": "create_bulk_craft_routes_api_v1_crafts_routes_bulk_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkCraftRouteCreateDTO"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkCraftRouteOperationResultDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/crafts/routes/craft/{craft_code}": {"get": {"tags": ["crafts"], "summary": "Get Craft Routes", "description": "Get all routes for a specific craft.", "operationId": "get_craft_routes_api_v1_crafts_routes_craft__craft_code__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "craft_code", "in": "path", "required": true, "schema": {"type": "string", "title": "Craft Code"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CraftRouteListDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/crafts/routes/skill/{skill_code}": {"get": {"tags": ["crafts"], "summary": "Get Skill Routes", "description": "Get all routes that use a specific skill.", "operationId": "get_skill_routes_api_v1_crafts_routes_skill__skill_code__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "skill_code", "in": "path", "required": true, "schema": {"type": "string", "title": "Skill Code"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CraftRouteListDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/crafts/routes/{route_id}": {"put": {"tags": ["crafts"], "summary": "Update Craft Route", "description": "Update craft route.", "operationId": "update_craft_route_api_v1_crafts_routes__route_id__put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "route_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Route Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CraftRouteUpdateDTO"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CraftRouteResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["crafts"], "summary": "Delete Craft Route", "description": "Delete craft route.", "operationId": "delete_craft_route_api_v1_crafts_routes__route_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "route_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Route Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/crafts/routes/reorder/{craft_code}": {"post": {"tags": ["crafts"], "summary": "Reorder Craft Routes", "description": "Reorder routes for a craft.", "operationId": "reorder_craft_routes_api_v1_crafts_routes_reorder__craft_code__post", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "craft_code", "in": "path", "required": true, "schema": {"type": "string", "title": "Craft Code"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "additionalProperties": true}, "title": "Route Orders"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CraftOperationResultDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/orders/": {"post": {"tags": ["orders"], "summary": "Create Order", "description": "Create a new order with order lines.", "operationId": "create_order_api_v1_orders__post", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderCreateDTO"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderDetailResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["orders"], "summary": "Get All Orders", "description": "Get all orders with optional filtering.", "operationId": "get_all_orders_api_v1_orders__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "search_term", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Search by order_no, skc_no, etc.", "title": "Search Term"}, "description": "Search by order_no, skc_no, etc."}, {"name": "status_filter", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by order status", "title": "Status Filter"}, "description": "Filter by order status"}, {"name": "owner_user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "Filter by owner user ID", "title": "Owner User Id"}, "description": "Filter by owner user ID"}, {"name": "current_craft", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by current craft", "title": "Current Craft"}, "description": "Filter by current craft"}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "Skip items", "default": 0, "title": "<PERSON><PERSON>"}, "description": "Skip items"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "description": "Limit items", "default": 100, "title": "Limit"}, "description": "Limit items"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderListDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/orders/{order_no}": {"get": {"tags": ["orders"], "summary": "Get Order By Order No Main", "description": "Get order by order number with order lines, crafts, and craft routes.", "operationId": "get_order_by_order_no_main_api_v1_orders__order_no__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_no", "in": "path", "required": true, "schema": {"type": "string", "title": "Order No"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderDetailResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/orders/{order_id}": {"put": {"tags": ["orders"], "summary": "Update Order", "description": "Update order (admin or owner only).", "operationId": "update_order_api_v1_orders__order_id__put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Order Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderUpdateDTO"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["orders"], "summary": "Delete Order", "description": "Delete order (admin only).", "operationId": "delete_order_api_v1_orders__order_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Order Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/orders/{order_id}/status": {"put": {"tags": ["orders"], "summary": "Update Order Status", "description": "Update order status.", "operationId": "update_order_status_api_v1_orders__order_id__status_put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Order Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderStatusUpdateDTO"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderOperationResultDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/orders/{order_no}/start": {"post": {"tags": ["orders"], "summary": "Start Order", "description": "Start an order - change status from PENDING to IN_PROGRESS.", "operationId": "start_order_api_v1_orders__order_no__start_post", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_no", "in": "path", "required": true, "schema": {"type": "string", "title": "Order No"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderOperationResultDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/orders/{order_id}/craft-progress": {"put": {"tags": ["orders"], "summary": "Update Craft Progress", "description": "Update order craft progress.", "operationId": "update_craft_progress_api_v1_orders__order_id__craft_progress_put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Order Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderCraftProgressDTO"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderOperationResultDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/orders/{order_id}/amount": {"put": {"tags": ["orders"], "summary": "Update Order Amount", "description": "Update order total amount.", "operationId": "update_order_amount_api_v1_orders__order_id__amount_put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Order Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderAmountUpdateDTO"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderOperationResultDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/orders/order-lines/bulk": {"post": {"tags": ["orders"], "summary": "Add Order Lines", "description": "Add multiple order lines to an existing order.", "operationId": "add_order_lines_api_v1_orders_order_lines_bulk_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkOrderLineCreateDTO"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/OrderLineResponseDTO"}, "type": "array", "title": "Response Add Order Lines Api V1 Orders Order Lines Bulk Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/orders/production": {"put": {"tags": ["orders"], "summary": "Update Production", "description": "Update production quantities for order lines.", "operationId": "update_production_api_v1_orders_production_put", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderProductionUpdateDTO"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderOperationResultDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/orders/statistics/summary": {"get": {"tags": ["orders"], "summary": "Get Order Statistics", "description": "Get order statistics summary.", "operationId": "get_order_statistics_api_v1_orders_statistics_summary_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "Filter by user ID", "title": "User Id"}, "description": "Filter by user ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderStatisticsDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/orders/dashboard/data": {"get": {"tags": ["orders"], "summary": "Get Dashboard Data", "description": "Get comprehensive dashboard data.", "operationId": "get_dashboard_data_api_v1_orders_dashboard_data_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "Filter by user ID", "title": "User Id"}, "description": "Filter by user ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderDashboardDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/order-parts/": {"post": {"tags": ["order-parts"], "summary": "Create Order Part", "description": "Create a new order part.", "operationId": "create_order_part_api_v1_order_parts__post", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderPartCreateDTO"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderPartResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["order-parts"], "summary": "Search Order Parts", "description": "Search order parts with optional filtering.", "operationId": "search_order_parts_api_v1_order_parts__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "search_term", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Search by order_part_no, part_name, etc.", "title": "Search Term"}, "description": "Search by order_part_no, part_name, etc."}, {"name": "order_no", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by order number", "title": "Order No"}, "description": "Filter by order number"}, {"name": "part_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by part type", "title": "Part Type"}, "description": "Filter by part type"}, {"name": "status_filter", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by part status", "title": "Status Filter"}, "description": "Filter by part status"}, {"name": "supervisor_user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "Filter by supervisor user ID", "title": "Supervisor User Id"}, "description": "Filter by supervisor user ID"}, {"name": "color", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by color", "title": "Color"}, "description": "Filter by color"}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "Skip items", "default": 0, "title": "<PERSON><PERSON>"}, "description": "Skip items"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "description": "Limit items", "default": 100, "title": "Limit"}, "description": "Limit items"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderPartListDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/order-parts/order/{order_no}": {"get": {"tags": ["order-parts"], "summary": "Get Order Parts By Order", "description": "Get all order parts for a specific order.", "operationId": "get_order_parts_by_order_api_v1_order_parts_order__order_no__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_no", "in": "path", "required": true, "schema": {"type": "string", "title": "Order No"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrderPartResponseDTO"}, "title": "Response Get Order Parts By Order Api V1 Order Parts Order  Order No  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/order-parts/{order_part_id}": {"get": {"tags": ["order-parts"], "summary": "Get Order Part By Id", "description": "Get order part by ID.", "operationId": "get_order_part_by_id_api_v1_order_parts__order_part_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_part_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Order Part Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderPartResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["order-parts"], "summary": "Update Order Part", "description": "Update order part.", "operationId": "update_order_part_api_v1_order_parts__order_part_id__put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_part_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Order Part Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderPartUpdateDTO"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderPartResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["order-parts"], "summary": "Delete Order Part", "description": "Delete order part and all its bundles.", "operationId": "delete_order_part_api_v1_order_parts__order_part_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_part_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Order Part Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/order-parts/{order_part_id}/with-bundles": {"get": {"tags": ["order-parts"], "summary": "Get Order Part With Bundles", "description": "Get order part by ID with order bundles.", "operationId": "get_order_part_with_bundles_api_v1_order_parts__order_part_id__with_bundles_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_part_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Order Part Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderPartWithBundlesDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/order-parts/{order_part_id}/status": {"put": {"tags": ["order-parts"], "summary": "Update Order Part Status", "description": "Update order part status.", "operationId": "update_order_part_status_api_v1_order_parts__order_part_id__status_put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_part_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Order Part Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderPartStatusUpdateDTO"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderPartOperationResultDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/order-parts/bulk": {"post": {"tags": ["order-parts"], "summary": "Bulk Create Order Parts", "description": "Create multiple order parts at once.", "operationId": "bulk_create_order_parts_api_v1_order_parts_bulk_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkOrderPartCreateDTO"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/OrderPartResponseDTO"}, "type": "array", "title": "Response Bulk Create Order Parts Api V1 Order Parts Bulk Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/order-parts/statistics/summary": {"get": {"tags": ["order-parts"], "summary": "Get Order Part Statistics", "description": "Get order part statistics summary.", "operationId": "get_order_part_statistics_api_v1_order_parts_statistics_summary_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_no", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by order number", "title": "Order No"}, "description": "Filter by order number"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderPartStatisticsDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/order-bundles/": {"post": {"tags": ["order-bundles"], "summary": "Create Order Bundle", "description": "Create a new order bundle.", "operationId": "create_order_bundle_api_v1_order_bundles__post", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderBundleCreateDTO"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderBundleResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["order-bundles"], "summary": "Search Order Bundles", "description": "Search order bundles with optional filtering.", "operationId": "search_order_bundles_api_v1_order_bundles__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "search_term", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Search by order_bundle_no, order_part_no, etc.", "title": "Search Term"}, "description": "Search by order_bundle_no, order_part_no, etc."}, {"name": "order_no", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by order number", "title": "Order No"}, "description": "Filter by order number"}, {"name": "order_part_no", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by order part number", "title": "Order Part No"}, "description": "Filter by order part number"}, {"name": "size", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by size", "title": "Size"}, "description": "Filter by size"}, {"name": "status_filter", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by bundle status", "title": "Status Filter"}, "description": "Filter by bundle status"}, {"name": "color", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by color", "title": "Color"}, "description": "Filter by color"}, {"name": "cutter_user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "Filter by cutter user ID", "title": "Cutter User Id"}, "description": "Filter by cutter user ID"}, {"name": "sewer_user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "Filter by sewer user ID", "title": "Sewer User Id"}, "description": "Filter by sewer user ID"}, {"name": "qc_user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "Filter by QC user ID", "title": "Qc User Id"}, "description": "Filter by QC user ID"}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "Skip items", "default": 0, "title": "<PERSON><PERSON>"}, "description": "Skip items"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "description": "Limit items", "default": 100, "title": "Limit"}, "description": "Limit items"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderBundleListDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/order-bundles/order-part/{order_part_no}/order/{order_no}": {"get": {"tags": ["order-bundles"], "summary": "Get Order Bundles By Order Part", "description": "Get all order bundles for a specific order part.", "operationId": "get_order_bundles_by_order_part_api_v1_order_bundles_order_part__order_part_no__order__order_no__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_part_no", "in": "path", "required": true, "schema": {"type": "string", "title": "Order Part No"}}, {"name": "order_no", "in": "path", "required": true, "schema": {"type": "string", "title": "Order No"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrderBundleResponseDTO"}, "title": "Response Get Order Bundles By Order Part Api V1 Order Bundles Order Part  Order Part No  Order  Order No  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/order-bundles/order/{order_no}": {"get": {"tags": ["order-bundles"], "summary": "Get Order Bundles By Order", "description": "Get all order bundles for a specific order.", "operationId": "get_order_bundles_by_order_api_v1_order_bundles_order__order_no__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_no", "in": "path", "required": true, "schema": {"type": "string", "title": "Order No"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrderBundleResponseDTO"}, "title": "Response Get Order Bundles By Order Api V1 Order Bundles Order  Order No  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/order-bundles/{order_bundle_id}": {"get": {"tags": ["order-bundles"], "summary": "Get Order Bundle By Id", "description": "Get order bundle by ID.", "operationId": "get_order_bundle_by_id_api_v1_order_bundles__order_bundle_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_bundle_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Order Bundle Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderBundleResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["order-bundles"], "summary": "Update Order Bundle", "description": "Update order bundle.", "operationId": "update_order_bundle_api_v1_order_bundles__order_bundle_id__put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_bundle_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Order Bundle Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderBundleUpdateDTO"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderBundleResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["order-bundles"], "summary": "Delete Order Bundle", "description": "Delete order bundle.", "operationId": "delete_order_bundle_api_v1_order_bundles__order_bundle_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_bundle_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Order Bundle Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/order-bundles/{order_bundle_id}/status": {"put": {"tags": ["order-bundles"], "summary": "Update Order Bundle Status", "description": "Update order bundle status.", "operationId": "update_order_bundle_status_api_v1_order_bundles__order_bundle_id__status_put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_bundle_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Order Bundle Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderBundleStatusUpdateDTO"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderBundleOperationResultDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/order-bundles/{order_bundle_id}/production": {"put": {"tags": ["order-bundles"], "summary": "Update Order Bundle Production", "description": "Update order bundle production progress.", "operationId": "update_order_bundle_production_api_v1_order_bundles__order_bundle_id__production_put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_bundle_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Order Bundle Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderBundleProductionUpdateDTO"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderBundleOperationResultDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/order-bundles/bulk": {"post": {"tags": ["order-bundles"], "summary": "Bulk Create Order Bundles", "description": "Create multiple order bundles at once.", "operationId": "bulk_create_order_bundles_api_v1_order_bundles_bulk_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkOrderBundleCreateDTO"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/OrderBundleResponseDTO"}, "type": "array", "title": "Response Bulk Create Order Bundles Api V1 Order Bundles Bulk Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/order-bundles/statistics/summary": {"get": {"tags": ["order-bundles"], "summary": "Get Order Bundle Statistics", "description": "Get order bundle statistics summary.", "operationId": "get_order_bundle_statistics_api_v1_order_bundles_statistics_summary_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_no", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by order number", "title": "Order No"}, "description": "Filter by order number"}, {"name": "order_part_no", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by order part number", "title": "Order Part No"}, "description": "Filter by order part number"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderBundleStatisticsDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/orders/{order_no}/crafts": {"post": {"summary": "Create Order Crafts For Order", "description": "Create order crafts configuration for an order.", "operationId": "create_order_crafts_for_order_api_v1_orders__order_no__crafts_post", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_no", "in": "path", "required": true, "schema": {"type": "string", "title": "Order No"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderCraftWorkflowDTO"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrderCraftResponseDTO"}, "title": "Response Create Order Crafts For Order Api V1 Orders  Order No  Crafts Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"summary": "Get Order Crafts By Order", "description": "Get all order crafts for an order.", "operationId": "get_order_crafts_by_order_api_v1_orders__order_no__crafts_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_no", "in": "path", "required": true, "schema": {"type": "string", "title": "Order No"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrderCraftResponseDTO"}, "title": "Response Get Order Crafts By Order Api V1 Orders  Order No  Crafts Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/crafts/{order_craft_id}": {"get": {"summary": "Get Order Craft By Id", "description": "Get order craft by ID.", "operationId": "get_order_craft_by_id_api_v1_crafts__order_craft_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_craft_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Order Craft Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderCraftResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/crafts/{order_craft_id}/status": {"put": {"summary": "Update Order Craft Status", "description": "Update order craft status.", "operationId": "update_order_craft_status_api_v1_crafts__order_craft_id__status_put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_craft_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Order Craft Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderCraftStatusUpdateDTO"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderCraftOperationResultDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/craft-routes/{order_craft_route_id}/status": {"put": {"summary": "Update Order Craft Route Status", "description": "Update order craft route status.", "operationId": "update_order_craft_route_status_api_v1_craft_routes__order_craft_route_id__status_put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_craft_route_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Order Craft Route Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderCraftRouteStatusUpdateDTO"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderCraftOperationResultDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/orders/{order_no}/crafts/next": {"get": {"summary": "Get Next Craft For Order", "description": "Get the next pending craft for an order.", "operationId": "get_next_craft_for_order_api_v1_orders__order_no__crafts_next_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_no", "in": "path", "required": true, "schema": {"type": "string", "title": "Order No"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderCraftResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/orders/{order_no}/crafts/current": {"get": {"summary": "Get Current Craft For Order", "description": "Get the currently in-progress craft for an order.", "operationId": "get_current_craft_for_order_api_v1_orders__order_no__crafts_current_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_no", "in": "path", "required": true, "schema": {"type": "string", "title": "Order No"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderCraftResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/statistics": {"get": {"summary": "Get Order Craft Statistics", "description": "Get order craft statistics.", "operationId": "get_order_craft_statistics_api_v1_statistics_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_no", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by order number", "title": "Order No"}, "description": "Filter by order number"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderCraftStatisticsDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/craft-instances/": {"post": {"tags": ["craft-instances"], "summary": "Create Craft Instance", "description": "Create a new craft instance - worker completion record.", "operationId": "create_craft_instance_api_v1_craft_instances__post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CraftInstanceCreateDTO"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CraftInstanceOperationResultDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/craft-instances/qr-scan": {"post": {"tags": ["craft-instances"], "summary": "Qr <PERSON><PERSON>", "description": "Register completion via QR code scanning.", "operationId": "qr_scan_register_api_v1_craft_instances_qr_scan_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CraftInstanceQRScanDTO"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CraftInstanceOperationResultDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/craft-instances/search": {"get": {"tags": ["craft-instances"], "summary": "Search Craft Instances", "description": "Search craft instances with filters.", "operationId": "search_craft_instances_api_v1_craft_instances_search_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_no", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "订单号", "title": "Order No"}, "description": "订单号"}, {"name": "worker_user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "工人ID", "title": "Worker User Id"}, "description": "工人ID"}, {"name": "completion_granularity", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/CompletionGranularityDTO"}, {"type": "null"}], "description": "完成粒度", "title": "Completion Granularity"}, "description": "完成粒度"}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "状态", "title": "Status"}, "description": "状态"}, {"name": "settlement_status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/SettlementStatusDTO"}, {"type": "null"}], "description": "结算状态", "title": "Settlement Status"}, "description": "结算状态"}, {"name": "quality_level", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "质量等级", "title": "Quality Level"}, "description": "质量等级"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "description": "每页数量", "default": 50, "title": "Limit"}, "description": "每页数量"}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "偏移量", "default": 0, "title": "Offset"}, "description": "偏移量"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CraftInstanceListDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/craft-instances/{instance_id}": {"get": {"tags": ["craft-instances"], "summary": "Get Craft Instance", "description": "Get craft instance by ID.", "operationId": "get_craft_instance_api_v1_craft_instances__instance_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "instance_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Instance Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CraftInstanceResponseDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/craft-instances/{instance_id}/verify": {"post": {"tags": ["craft-instances"], "summary": "Verify Craft Instance", "description": "Verify a craft instance.", "operationId": "verify_craft_instance_api_v1_craft_instances__instance_id__verify_post", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "instance_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Instance Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CraftInstanceVerificationDTO"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CraftInstanceOperationResultDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/craft-instances/{instance_id}/reject": {"post": {"tags": ["craft-instances"], "summary": "Reject Craft Instance", "description": "Reject a craft instance.", "operationId": "reject_craft_instance_api_v1_craft_instances__instance_id__reject_post", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "instance_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Instance Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CraftInstanceRejectionDTO"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CraftInstanceOperationResultDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/craft-instances/statistics/overview": {"get": {"tags": ["craft-instances"], "summary": "Get Craft Instance Statistics", "description": "Get craft instance statistics.", "operationId": "get_craft_instance_statistics_api_v1_craft_instances_statistics_overview_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_no", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "订单号", "title": "Order No"}, "description": "订单号"}, {"name": "worker_user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "工人ID", "title": "Worker User Id"}, "description": "工人ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CraftInstanceStatisticsDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/available-registration/{order_no}": {"get": {"tags": ["available-registration"], "summary": "Get Available Registration Data", "description": "Get available registration data for an order - returns parts/bundles that can still be registered.", "operationId": "getAvailableRegistrationData", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_no", "in": "path", "required": true, "schema": {"type": "string", "title": "Order No"}}, {"name": "craft_route_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "筛选指定工艺路线ID", "title": "Craft Route Id"}, "description": "筛选指定工艺路线ID"}, {"name": "include_completed_routes", "in": "query", "required": false, "schema": {"type": "boolean", "description": "是否包含已完成的路线", "default": false, "title": "Include Completed Routes"}, "description": "是否包含已完成的路线"}, {"name": "granularity_filter", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "粒度过滤: order/part/bundle", "title": "Granularity Filter"}, "description": "粒度过滤: order/part/bundle"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AvailableRegistrationDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/available-registration/{order_no}/summary": {"get": {"tags": ["available-registration"], "summary": "Get Registration Summary", "description": "Get registration summary for an order - overview of completion status.", "operationId": "getRegistrationSummary", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "order_no", "in": "path", "required": true, "schema": {"type": "string", "title": "Order No"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegistrationSummaryDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/": {"get": {"summary": "Root", "description": "Root endpoint.", "operationId": "root__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/health": {"get": {"summary": "Health Check", "description": "Health check endpoint.", "operationId": "health_check_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"AddUserToFactoryDTO": {"properties": {"user_id": {"type": "integer", "title": "User Id", "description": "User ID to add"}, "factory_role": {"type": "string", "title": "Factory Role", "description": "Initial role in factory", "default": "WORKER"}, "start_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Start Date", "description": "Start date (ISO format)"}}, "type": "object", "required": ["user_id"], "title": "AddUserToFactoryDTO", "description": "DTO for adding user to factory."}, "AddUserWithFactoryResponseDTO": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "Whether operation succeeded"}, "message": {"type": "string", "title": "Message", "description": "Result message"}, "user_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "User Id", "description": "User ID (if created or found)"}, "user_created": {"type": "boolean", "title": "User Created", "description": "Whether a new user was created"}, "factory_relationship_created": {"type": "boolean", "title": "Factory Relationship Created", "description": "Whether factory relationship was created"}, "skills_assigned": {"type": "integer", "title": "Skills Assigned", "description": "Number of skills successfully assigned", "default": 0}, "details": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Details", "description": "Additional operation details"}}, "type": "object", "required": ["success", "message", "user_created", "factory_relationship_created"], "title": "AddUserWithFactoryResponseDTO", "description": "Response DTO for add user with factory operation."}, "AddUsersToFactoryDTO": {"properties": {"users": {"items": {"$ref": "#/components/schemas/AddUserToFactoryDTO"}, "type": "array", "title": "Users", "description": "List of users to add"}}, "type": "object", "required": ["users"], "title": "AddUsersToFactoryDTO", "description": "DTO for adding multiple users to factory."}, "AssignSkillDTO": {"properties": {"user_id": {"type": "integer", "title": "User Id", "description": "User ID"}, "skill_id": {"type": "integer", "title": "Skill Id", "description": "Skill ID"}, "proficiency_level": {"type": "string", "title": "Proficiency Level", "description": "Initial proficiency level", "default": "BEGINNER"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "Additional notes"}}, "type": "object", "required": ["user_id", "skill_id"], "title": "AssignSkillDTO", "description": "DTO for assigning skill to user."}, "AssignSkillsDTO": {"properties": {"user_id": {"type": "integer", "title": "User Id", "description": "User ID"}, "skills": {"items": {"$ref": "#/components/schemas/AssignSkillDTO"}, "type": "array", "title": "Skills", "description": "List of skills to assign"}}, "type": "object", "required": ["user_id", "skills"], "title": "AssignSkillsDTO", "description": "DTO for assigning multiple skills to user."}, "AvailableBundleDTO": {"properties": {"order_bundle_no": {"type": "string", "title": "Order Bundle No", "description": "订单扎号"}, "total_quantity": {"type": "integer", "title": "Total Quantity", "description": "扎总数量"}, "registered_quantity": {"type": "integer", "title": "Registered Quantity", "description": "已登记数量", "default": 0}, "available_quantity": {"type": "integer", "title": "Available Quantity", "description": "可登记数量"}}, "type": "object", "required": ["order_bundle_no", "total_quantity", "available_quantity"], "title": "AvailableBundleDTO", "description": "可登记的订单扎信息"}, "AvailableCraftRouteDTO": {"properties": {"order_craft_route_id": {"type": "integer", "title": "Order Craft Route Id", "description": "订单工艺路线ID"}, "craft_route_name": {"type": "string", "title": "Craft Route Name", "description": "工艺路线名称"}, "skill_code": {"type": "string", "title": "Skill Code", "description": "技能代码"}, "skill_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Skill Name", "description": "技能名称"}, "route_status": {"type": "string", "title": "Route Status", "description": "路线状态"}, "total_quantity": {"type": "integer", "title": "Total Quantity", "description": "路线总数量"}, "registered_quantity": {"type": "integer", "title": "Registered Quantity", "description": "已登记数量", "default": 0}, "available_quantity": {"type": "integer", "title": "Available Quantity", "description": "可登记数量"}, "supports_order_level": {"type": "boolean", "title": "Supports Order Level", "description": "支持整单级别登记", "default": true}, "supports_part_level": {"type": "boolean", "title": "Supports Part Level", "description": "支持部位级别登记", "default": true}, "supports_bundle_level": {"type": "boolean", "title": "Supports Bundle Level", "description": "支持扎级别登记", "default": true}}, "type": "object", "required": ["order_craft_route_id", "craft_route_name", "skill_code", "route_status", "total_quantity", "available_quantity"], "title": "AvailableCraftRouteDTO", "description": "可登记的工艺路线信息"}, "AvailableFactoriesDTO": {"properties": {"factories": {"items": {"$ref": "#/components/schemas/AvailableFactoryDTO"}, "type": "array", "title": "Factories"}, "current_factory_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Current Factory Id"}}, "type": "object", "required": ["factories"], "title": "AvailableFactoriesDTO", "description": "DTO for list of available factories."}, "AvailableFactoryDTO": {"properties": {"factory_id": {"type": "integer", "title": "Factory Id"}, "factory_name": {"type": "string", "title": "Factory Name"}, "factory_code": {"type": "string", "title": "Factory Code"}, "department_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Department Id"}, "department_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Department Name"}, "role": {"type": "string", "title": "Role"}, "employee_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Employee Id"}, "position": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Position"}, "is_manager": {"type": "boolean", "title": "Is Manager"}, "is_current": {"type": "boolean", "title": "Is Current"}}, "type": "object", "required": ["factory_id", "factory_name", "factory_code", "role", "is_manager", "is_current"], "title": "AvailableFactoryDTO", "description": "DTO for available factory in switch list."}, "AvailablePartDTO": {"properties": {"order_part_no": {"type": "string", "title": "Order Part No", "description": "订单部位号"}, "total_quantity": {"type": "integer", "title": "Total Quantity", "description": "部位总数量"}, "registered_quantity": {"type": "integer", "title": "Registered Quantity", "description": "已登记数量", "default": 0}, "available_quantity": {"type": "integer", "title": "Available Quantity", "description": "可登记数量"}, "order_bundles": {"items": {"$ref": "#/components/schemas/AvailableBundleDTO"}, "type": "array", "title": "Order Bundles", "description": "该部位下的可登记扎列表"}}, "type": "object", "required": ["order_part_no", "total_quantity", "available_quantity"], "title": "AvailablePartDTO", "description": "可登记的订单部位信息"}, "AvailableRegistrationDTO": {"properties": {"order_no": {"type": "string", "title": "Order No", "description": "订单号"}, "order_status": {"type": "string", "title": "Order Status", "description": "订单状态"}, "total_quantity": {"type": "integer", "title": "Total Quantity", "description": "订单总数量"}, "registered_quantity": {"type": "integer", "title": "Registered Quantity", "description": "已登记数量", "default": 0}, "available_quantity": {"type": "integer", "title": "Available Quantity", "description": "可登记数量"}, "craft_routes": {"items": {"$ref": "#/components/schemas/AvailableCraftRouteDTO"}, "type": "array", "title": "Craft Routes", "description": "可登记的工艺路线列表"}, "order_parts": {"items": {"$ref": "#/components/schemas/AvailablePartDTO"}, "type": "array", "title": "Order Parts", "description": "可登记的订单部位列表"}}, "type": "object", "required": ["order_no", "order_status", "total_quantity", "available_quantity"], "title": "AvailableRegistrationDTO", "description": "订单可登记数据"}, "AvailableUsersDTO": {"properties": {"users": {"items": {"$ref": "#/components/schemas/UserSummaryDTO"}, "type": "array", "title": "Users", "description": "List of available users"}, "total": {"type": "integer", "title": "Total", "description": "Total number of available users"}}, "type": "object", "required": ["users", "total"], "title": "AvailableUsersDTO", "description": "DTO for available users (not in factory)."}, "BindUserRoleDTO": {"properties": {"user_id": {"type": "integer", "title": "User Id", "description": "User ID"}, "role_id": {"type": "integer", "title": "Role Id", "description": "Role ID to assign"}}, "type": "object", "required": ["user_id", "role_id"], "title": "BindUserRoleDTO", "description": "DTO for binding system role to user."}, "Body_login_api_v1_auth_token_post": {"properties": {"grant_type": {"anyOf": [{"type": "string", "pattern": "password"}, {"type": "null"}], "title": "Grant Type"}, "username": {"type": "string", "title": "Username"}, "password": {"type": "string", "title": "Password"}, "scope": {"type": "string", "title": "<PERSON><PERSON>", "default": ""}, "client_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Client Id"}, "client_secret": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Client Secret"}}, "type": "object", "required": ["username", "password"], "title": "Body_login_api_v1_auth_token_post"}, "BulkCraftRouteCreateDTO": {"properties": {"craft_code": {"type": "string", "title": "Craft Code", "description": "Craft code for all routes"}, "routes": {"items": {"$ref": "#/components/schemas/CraftRouteCreateDTO"}, "type": "array", "title": "Routes", "description": "List of routes to create"}}, "type": "object", "required": ["craft_code", "routes"], "title": "BulkCraftRouteCreateDTO", "description": "DTO for creating multiple craft routes at once."}, "BulkCraftRouteOperationResultDTO": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "Whether operation succeeded overall"}, "message": {"type": "string", "title": "Message", "description": "Overall result message"}, "results": {"items": {"$ref": "#/components/schemas/CraftOperationResultDTO"}, "type": "array", "title": "Results", "description": "Individual operation results"}, "successful_count": {"type": "integer", "title": "Successful Count", "description": "Number of successful operations"}, "failed_count": {"type": "integer", "title": "Failed Count", "description": "Number of failed operations"}}, "type": "object", "required": ["success", "message", "results", "successful_count", "failed_count"], "title": "BulkCraftRouteOperationResultDTO", "description": "DTO for bulk craft route operation results."}, "BulkOrderBundleCreateDTO": {"properties": {"order_no": {"type": "string", "title": "Order No", "description": "订单号"}, "order_part_no": {"type": "string", "title": "Order Part No", "description": "订单部位号"}, "order_bundles": {"items": {"$ref": "#/components/schemas/OrderBundleCreateDTO"}, "type": "array", "minItems": 1, "title": "Order Bundles", "description": "订单扎列表"}}, "type": "object", "required": ["order_no", "order_part_no", "order_bundles"], "title": "BulkOrderBundleCreateDTO", "description": "DTO for creating multiple order bundles."}, "BulkOrderLineCreateDTO": {"properties": {"order_no": {"type": "string", "title": "Order No", "description": "订单号"}, "order_lines": {"items": {"$ref": "#/components/schemas/OrderLineCreateDTO"}, "type": "array", "minItems": 1, "title": "Order Lines", "description": "订单行列表"}}, "type": "object", "required": ["order_no", "order_lines"], "title": "BulkOrderLineCreateDTO", "description": "DTO for creating multiple order lines."}, "BulkOrderPartCreateDTO": {"properties": {"order_no": {"type": "string", "title": "Order No", "description": "订单号"}, "order_parts": {"items": {"$ref": "#/components/schemas/OrderPartCreateDTO"}, "type": "array", "minItems": 1, "title": "Order Parts", "description": "订单部位列表"}}, "type": "object", "required": ["order_no", "order_parts"], "title": "BulkOrderPartCreateDTO", "description": "DTO for creating multiple order parts."}, "BundleStatusEnum": {"type": "string", "enum": ["planned", "cutting", "cut_completed", "sewing", "sew_completed", "quality_check", "completed", "rework", "on_hold", "cancelled"], "title": "BundleStatusEnum", "description": "Bundle status enumeration for DTOs."}, "CertifySkillDTO": {"properties": {"user_factory_skill_id": {"type": "integer", "title": "User Factory Skill Id", "description": "User factory skill ID"}, "certification_expires": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Certification Expires", "description": "Certification expiry date (ISO format)"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "Certification notes"}}, "type": "object", "required": ["user_factory_skill_id"], "title": "CertifySkillDTO", "description": "DTO for certifying user in skill."}, "CompletionGranularityDTO": {"type": "string", "enum": ["bundle", "bed", "order"], "title": "CompletionGranularityDTO", "description": "完成粒度枚举"}, "CraftCreateDTO": {"properties": {"code": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Code", "description": "Unique craft code"}, "name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Name", "description": "Craft name"}, "priority": {"type": "integer", "minimum": 0.0, "title": "Priority", "description": "Craft priority (higher number = higher priority)", "default": 0}, "enabled": {"type": "boolean", "title": "Enabled", "description": "Whether the craft is enabled", "default": true}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "Craft description"}}, "type": "object", "required": ["code", "name"], "title": "CraftCreateDTO", "description": "DTO for creating a new craft."}, "CraftInstanceCreateDTO": {"properties": {"order_craft_route_id": {"type": "integer", "title": "Order Craft Route Id", "description": "订单工艺路线ID"}, "completion_granularity": {"$ref": "#/components/schemas/CompletionGranularityDTO", "description": "完成粒度"}, "order_no": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Order No", "description": "订单号"}, "order_part_nos": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Order Part Nos", "description": "订单部位号列表 (床级别或扎级别时填写)"}, "order_bundle_nos": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Order Bundle Nos", "description": "订单扎号列表 (扎级别时填写)"}, "order_part_no": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Order Part No", "description": "订单部位号 (兼容性字段)"}, "order_bundle_no": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Order Bundle No", "description": "订单扎号 (兼容性字段)"}, "worker_user_id": {"type": "integer", "title": "Worker User Id", "description": "完成工人ID"}, "completed_quantity": {"type": "integer", "minimum": 0.0, "title": "Completed Quantity", "description": "完成数量"}, "quality_level": {"anyOf": [{"type": "string", "maxLength": 10}, {"type": "null"}], "title": "Quality Level", "description": "质量等级 A/B/C"}, "started_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Started At", "description": "开始时间"}, "qr_code_scanned": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Qr Code Scanned", "description": "扫描的二维码内容"}, "scan_location": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Scan Location", "description": "扫码位置"}, "device_info": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Device Info", "description": "设备信息"}, "measurement_data": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Measurement Data", "description": "测量数据"}, "registration_data": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Registration Data", "description": "登记数据"}, "notes": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Notes", "description": "备注"}}, "type": "object", "required": ["order_craft_route_id", "completion_granularity", "order_no", "worker_user_id", "completed_quantity"], "title": "CraftInstanceCreateDTO", "description": "DTO for creating craft instances - worker completion records."}, "CraftInstanceListDTO": {"properties": {"instances": {"items": {"$ref": "#/components/schemas/CraftInstanceResponseDTO"}, "type": "array", "title": "Instances", "description": "实例列表"}, "total": {"type": "integer", "title": "Total", "description": "总数量"}, "total_quantity": {"type": "integer", "title": "Total Quantity", "description": "总完成数量"}}, "type": "object", "required": ["instances", "total", "total_quantity"], "title": "CraftInstanceListDTO", "description": "DTO for craft instance list response."}, "CraftInstanceOperationResultDTO": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "操作是否成功"}, "message": {"type": "string", "title": "Message", "description": "结果消息"}, "instance_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Instance Id", "description": "实例ID"}, "details": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Details", "description": "详细信息"}}, "type": "object", "required": ["success", "message"], "title": "CraftInstanceOperationResultDTO", "description": "DTO for craft instance operation results."}, "CraftInstanceQRScanDTO": {"properties": {"qr_code_content": {"type": "string", "minLength": 1, "title": "Qr Code Content", "description": "二维码内容"}, "scan_location": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Scan Location", "description": "扫码位置"}, "device_info": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Device Info", "description": "设备信息"}, "completed_quantity": {"type": "integer", "minimum": 1.0, "title": "Completed Quantity", "description": "完成数量"}, "quality_level": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Quality Level", "description": "质量等级"}, "measurement_data": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Measurement Data", "description": "测量数据"}, "registration_data": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Registration Data", "description": "登记数据"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "备注"}}, "type": "object", "required": ["qr_code_content", "completed_quantity"], "title": "CraftInstanceQRScanDTO", "description": "DTO for QR code scanning registration."}, "CraftInstanceRejectionDTO": {"properties": {"rejected_by_user_id": {"type": "integer", "title": "Rejected By User Id", "description": "拒绝人ID"}, "reason": {"type": "string", "maxLength": 1000, "minLength": 1, "title": "Reason", "description": "拒绝原因"}}, "type": "object", "required": ["rejected_by_user_id", "reason"], "title": "CraftInstanceRejectionDTO", "description": "DTO for rejecting craft instances."}, "CraftInstanceResponseDTO": {"properties": {"id": {"type": "integer", "title": "Id", "description": "实例ID"}, "factory_id": {"type": "integer", "title": "Factory Id", "description": "工厂ID"}, "order_craft_route_id": {"type": "integer", "title": "Order Craft Route Id", "description": "订单工艺路线ID"}, "completion_granularity": {"$ref": "#/components/schemas/CompletionGranularityDTO", "description": "完成粒度"}, "order_no": {"type": "string", "title": "Order No", "description": "订单号"}, "order_part_nos": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Order Part Nos", "description": "订单部位号列表"}, "order_bundle_nos": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Order Bundle Nos", "description": "订单扎号列表"}, "order_part_no": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Order Part No", "description": "主要订单部位号"}, "order_bundle_no": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Order Bundle No", "description": "主要订单扎号"}, "worker_user_id": {"type": "integer", "title": "Worker User Id", "description": "完成工人ID"}, "worker_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Worker Name", "description": "工人姓名"}, "completed_quantity": {"type": "integer", "title": "Completed Quantity", "description": "完成数量"}, "quality_level": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Quality Level", "description": "质量等级"}, "status": {"type": "string", "title": "Status", "description": "状态"}, "settlement_status": {"$ref": "#/components/schemas/SettlementStatusDTO", "description": "结算状态"}, "started_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Started At", "description": "开始时间"}, "completed_at": {"type": "string", "format": "date-time", "title": "Completed At", "description": "完成时间"}, "work_duration_minutes": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Work Duration Minutes", "description": "工作时长(分钟)"}, "qr_code_scanned": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Qr Code Scanned", "description": "扫描的二维码内容"}, "scan_location": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Scan Location", "description": "扫码位置"}, "device_info": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Device Info", "description": "设备信息"}, "measurement_data": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Measurement Data", "description": "测量数据"}, "registration_data": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Registration Data", "description": "登记数据"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "备注"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "更新时间"}, "craft_route_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Craft Route Name", "description": "工艺路线名称"}, "skill_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Skill Code", "description": "技能代码"}, "skill_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Skill Name", "description": "技能名称"}}, "type": "object", "required": ["id", "factory_id", "order_craft_route_id", "completion_granularity", "order_no", "worker_user_id", "completed_quantity", "status", "settlement_status", "completed_at", "created_at", "updated_at"], "title": "CraftInstanceResponseDTO", "description": "DTO for craft instance response."}, "CraftInstanceStatisticsDTO": {"properties": {"total_instances": {"type": "integer", "title": "Total Instances", "description": "总实例数"}, "total_quantity": {"type": "integer", "title": "Total Quantity", "description": "总完成数量"}, "total_workers": {"type": "integer", "title": "Total Workers", "description": "参与工人数"}, "average_quality_score": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Average Quality Score", "description": "平均质量分数"}, "status_breakdown": {"additionalProperties": {"type": "integer"}, "type": "object", "title": "Status Breakdown", "description": "状态分解"}, "settlement_breakdown": {"additionalProperties": {"type": "integer"}, "type": "object", "title": "Settlement Breakdown", "description": "结算状态分解"}, "granularity_breakdown": {"additionalProperties": {"type": "integer"}, "type": "object", "title": "Granularity Breakdown", "description": "粒度分解"}, "quality_breakdown": {"additionalProperties": {"type": "integer"}, "type": "object", "title": "Quality Breakdown", "description": "质量等级分解"}, "daily_completion_trend": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Daily Completion Trend", "description": "每日完成趋势"}}, "type": "object", "required": ["total_instances", "total_quantity", "total_workers", "status_breakdown", "settlement_breakdown", "granularity_breakdown", "quality_breakdown", "daily_completion_trend"], "title": "CraftInstanceStatisticsDTO", "description": "DTO for craft instance statistics."}, "CraftInstanceVerificationDTO": {"properties": {"verified_by_user_id": {"type": "integer", "title": "Verified By User Id", "description": "验证人ID"}, "notes": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Notes", "description": "验证备注"}}, "type": "object", "required": ["verified_by_user_id"], "title": "CraftInstanceVerificationDTO", "description": "DTO for verifying craft instances."}, "CraftListDTO": {"properties": {"crafts": {"items": {"$ref": "#/components/schemas/CraftResponseDTO"}, "type": "array", "title": "Crafts", "description": "List of crafts"}, "total": {"type": "integer", "title": "Total", "description": "Total number of crafts"}}, "type": "object", "required": ["crafts", "total"], "title": "CraftListDTO", "description": "DTO for craft list response."}, "CraftOperationResultDTO": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "Whether operation succeeded"}, "message": {"type": "string", "title": "Message", "description": "Result message"}, "craft_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Craft Id", "description": "Craft ID affected"}, "craft_route_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Craft Route Id", "description": "Craft route ID affected"}, "details": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Details", "description": "Additional operation details"}}, "type": "object", "required": ["success", "message"], "title": "CraftOperationResultDTO", "description": "DTO for craft operation results."}, "CraftResponseDTO": {"properties": {"code": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Code", "description": "Unique craft code"}, "name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Name", "description": "Craft name"}, "priority": {"type": "integer", "minimum": 0.0, "title": "Priority", "description": "Craft priority (higher number = higher priority)", "default": 0}, "enabled": {"type": "boolean", "title": "Enabled", "description": "Whether the craft is enabled", "default": true}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "Craft description"}, "id": {"type": "integer", "title": "Id", "description": "Craft ID"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "Creation timestamp"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "Last update timestamp"}}, "type": "object", "required": ["code", "name", "id", "created_at", "updated_at"], "title": "CraftResponseDTO", "description": "DTO for craft response."}, "CraftRouteCreateDTO": {"properties": {"craft_code": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Craft Code", "description": "Craft code"}, "skill_code": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Skill Code", "description": "Skill code"}, "code": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Code", "description": "Unique code within craft"}, "name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Name", "description": "Display name for this route"}, "order": {"type": "integer", "minimum": 0.0, "title": "Order", "description": "Order in the craft workflow", "default": 0}, "measurement_types": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Measurement Types", "description": "Available measurement types"}, "registration_types": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Registration Types", "description": "Available registration types"}, "notes": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Notes", "description": "Additional notes"}, "is_required": {"type": "boolean", "title": "Is Required", "description": "Whether this step is required", "default": true}}, "type": "object", "required": ["craft_code", "skill_code", "code", "name"], "title": "CraftRouteCreateDTO", "description": "DTO for creating a new craft route."}, "CraftRouteDetailDTO": {"properties": {"id": {"type": "integer", "title": "Id", "description": "Craft route ID"}, "craft_code": {"type": "string", "title": "Craft Code", "description": "Craft code"}, "skill_code": {"type": "string", "title": "Skill Code", "description": "Skill code"}, "skill_name": {"type": "string", "title": "Skill Name", "description": "Skill name"}, "code": {"type": "string", "title": "Code", "description": "Unique code within craft"}, "name": {"type": "string", "title": "Name", "description": "Display name for this route"}, "order": {"type": "integer", "title": "Order", "description": "Order in the craft workflow"}, "measurement_types": {"items": {"type": "string"}, "type": "array", "title": "Measurement Types", "description": "Available measurement types"}, "registration_types": {"items": {"type": "string"}, "type": "array", "title": "Registration Types", "description": "Available registration types"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "Additional notes"}, "is_required": {"type": "boolean", "title": "Is Required", "description": "Whether this step is required"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "Creation timestamp"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "Last update timestamp"}}, "type": "object", "required": ["id", "craft_code", "skill_code", "skill_name", "code", "name", "order", "measurement_types", "registration_types", "is_required", "created_at", "updated_at"], "title": "CraftRouteDetailDTO", "description": "DTO for detailed craft route with skill information."}, "CraftRouteListDTO": {"properties": {"routes": {"items": {"$ref": "#/components/schemas/CraftRouteDetailDTO"}, "type": "array", "title": "Routes", "description": "List of craft routes"}, "total": {"type": "integer", "title": "Total", "description": "Total number of routes"}}, "type": "object", "required": ["routes", "total"], "title": "CraftRouteListDTO", "description": "DTO for craft route list response."}, "CraftRouteResponseDTO": {"properties": {"craft_code": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Craft Code", "description": "Craft code"}, "skill_code": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Skill Code", "description": "Skill code"}, "code": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Code", "description": "Unique code within craft"}, "name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Name", "description": "Display name for this route"}, "order": {"type": "integer", "minimum": 0.0, "title": "Order", "description": "Order in the craft workflow", "default": 0}, "measurement_types": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Measurement Types", "description": "Available measurement types"}, "registration_types": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Registration Types", "description": "Available registration types"}, "notes": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Notes", "description": "Additional notes"}, "is_required": {"type": "boolean", "title": "Is Required", "description": "Whether this step is required", "default": true}, "id": {"type": "integer", "title": "Id", "description": "Craft route ID"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "Creation timestamp"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "Last update timestamp"}}, "type": "object", "required": ["craft_code", "skill_code", "code", "name", "id", "created_at", "updated_at"], "title": "CraftRouteResponseDTO", "description": "DTO for craft route response."}, "CraftRouteUpdateDTO": {"properties": {"code": {"anyOf": [{"type": "string", "maxLength": 50, "minLength": 1}, {"type": "null"}], "title": "Code", "description": "Unique code within craft"}, "name": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 1}, {"type": "null"}], "title": "Name", "description": "Display name for this route"}, "order": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Order", "description": "Order in the craft workflow"}, "measurement_types": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Measurement Types", "description": "Available measurement types"}, "registration_types": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Registration Types", "description": "Available registration types"}, "notes": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Notes", "description": "Additional notes"}, "is_required": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Required", "description": "Whether this step is required"}}, "type": "object", "title": "CraftRouteUpdateDTO", "description": "DTO for updating a craft route."}, "CraftUpdateDTO": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 1}, {"type": "null"}], "title": "Name", "description": "Craft name"}, "priority": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Priority", "description": "Craft priority"}, "enabled": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Enabled", "description": "Whether the craft is enabled"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "Craft description"}}, "type": "object", "title": "CraftUpdateDTO", "description": "DTO for updating a craft."}, "CraftWithRoutesDTO": {"properties": {"code": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Code", "description": "Unique craft code"}, "name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Name", "description": "Craft name"}, "priority": {"type": "integer", "minimum": 0.0, "title": "Priority", "description": "Craft priority (higher number = higher priority)", "default": 0}, "enabled": {"type": "boolean", "title": "Enabled", "description": "Whether the craft is enabled", "default": true}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "Craft description"}, "id": {"type": "integer", "title": "Id", "description": "Craft ID"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "Creation timestamp"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "Last update timestamp"}, "routes": {"items": {"$ref": "#/components/schemas/CraftRouteDetailDTO"}, "type": "array", "title": "Routes", "description": "Craft routes ordered by order field"}}, "type": "object", "required": ["code", "name", "id", "created_at", "updated_at", "routes"], "title": "CraftWithRoutesDTO", "description": "DTO for craft with its routes."}, "CreateUserWithFactoryDTO": {"properties": {"username": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Username", "description": "Username"}, "email": {"type": "string", "title": "Email", "description": "Email address"}, "password": {"type": "string", "minLength": 6, "title": "Password", "description": "Password"}, "full_name": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Full Name", "description": "Full name"}, "phone": {"anyOf": [{"type": "string", "pattern": "^1[3-9]\\d{9}$"}, {"type": "null"}], "title": "Phone", "description": "Phone number (Chinese format)"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Whether user is active", "default": true}, "role_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Role Id", "description": "System role ID to assign"}, "factory_role": {"type": "string", "title": "Factory Role", "description": "Role in factory (WOR<PERSON><PERSON>, S<PERSON><PERSON><PERSON><PERSON>R, MANAGER, ADMIN)", "default": "WORKER"}, "department_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Department Id", "description": "Department ID in factory"}, "start_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Start Date", "description": "Start date (ISO format)"}, "position": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Position", "description": "Position/job title"}, "employee_id": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Employee Id", "description": "Employee ID"}, "skills": {"items": {"$ref": "#/components/schemas/UserSkillCreateDTO"}, "type": "array", "title": "Skills", "description": "Initial skills to assign"}}, "type": "object", "required": ["username", "email", "password"], "title": "CreateUserWithFactoryDTO", "description": "DTO for creating a new user with factory relationship and skills."}, "FactoryContextDTO": {"properties": {"factory_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Factory Id"}, "factory_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Factory Name"}, "department_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Department Id"}, "role": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Role"}, "is_manager": {"type": "boolean", "title": "Is Manager", "default": false}}, "type": "object", "title": "FactoryContextDTO", "description": "Factory context information for user session."}, "FactoryJoinApprovalDTO": {"properties": {"user_factory_id": {"type": "integer", "title": "User Factory Id", "description": "UserFactory record ID"}, "action": {"type": "string", "title": "Action", "description": "approve or reject"}, "employee_id": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Employee Id", "description": "Employee ID (if approved)"}, "position": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Position", "description": "Position title (if approved)"}, "department_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Department Id", "description": "Department assignment (if approved)"}, "role": {"anyOf": [{"$ref": "#/components/schemas/UserFactoryRoleEnum"}, {"type": "null"}], "description": "User role in factory", "default": "worker"}, "reason": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Reason", "description": "Rejection reason (if rejected)"}, "start_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Start Date", "description": "Start date (if approved)"}}, "type": "object", "required": ["user_factory_id", "action"], "title": "FactoryJoinApprovalDTO", "description": "DTO for approving/rejecting factory join requests."}, "FactoryJoinRequestDTO": {"properties": {"factory_id": {"type": "integer", "title": "Factory Id", "description": "Factory ID to join"}, "department_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Department Id", "description": "Optional department ID"}, "message": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Message", "description": "Optional message to manager"}}, "type": "object", "required": ["factory_id"], "title": "FactoryJoinRequestDTO", "description": "DTO for requesting to join a factory."}, "FactoryUserDTO": {"properties": {"user": {"$ref": "#/components/schemas/UserSummaryDTO", "description": "User information"}, "factory_role": {"type": "string", "title": "Factory Role", "description": "Role in factory (WOR<PERSON><PERSON>, S<PERSON><PERSON><PERSON><PERSON>R, MANAGER, ADMIN)"}, "factory_status": {"type": "string", "title": "Factory Status", "description": "Status in factory (APPROVED, SUSPENDED, etc.)"}, "joined_at": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Joined At", "description": "Date joined factory"}, "skills": {"items": {"$ref": "#/components/schemas/UserFactorySkillDTO"}, "type": "array", "title": "Skills", "description": "User's skills in this factory"}, "skills_count": {"type": "integer", "title": "Skills Count", "description": "Total number of skills", "default": 0}, "certified_skills_count": {"type": "integer", "title": "Certified Skills Count", "description": "Number of certified skills", "default": 0}}, "type": "object", "required": ["user", "factory_role", "factory_status"], "title": "FactoryUserDTO", "description": "DTO for factory user with factory-specific information."}, "FactoryUserListDTO": {"properties": {"users": {"items": {"$ref": "#/components/schemas/FactoryUserDTO"}, "type": "array", "title": "Users", "description": "List of factory users"}, "total": {"type": "integer", "title": "Total", "description": "Total number of users"}, "factory_id": {"type": "integer", "title": "Factory Id", "description": "Factory ID"}, "factory_name": {"type": "string", "title": "Factory Name", "description": "Factory name"}}, "type": "object", "required": ["users", "total", "factory_id", "factory_name"], "title": "FactoryUserListDTO", "description": "DTO for factory user list response."}, "GenerateImageCodeDTO": {"properties": {"session_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Id", "description": "Optional session ID, will generate if not provided"}}, "type": "object", "title": "GenerateImageCodeDTO", "description": "DTO for generating image validation code."}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "ImageCodeResponseDTO": {"properties": {"session_id": {"type": "string", "title": "Session Id"}, "image_base64": {"type": "string", "title": "Image Base64"}, "expires_in_seconds": {"type": "integer", "title": "Expires In Seconds"}}, "type": "object", "required": ["session_id", "image_base64", "expires_in_seconds"], "title": "ImageCodeResponseDTO", "description": "Response DTO for image validation code."}, "MyFactoriesDTO": {"properties": {"active_factories": {"items": {"$ref": "#/components/schemas/UserFactoryResponseDTO"}, "type": "array", "title": "Active Factories"}, "pending_requests": {"items": {"$ref": "#/components/schemas/UserFactoryResponseDTO"}, "type": "array", "title": "Pending Requests"}, "managed_factories": {"items": {"$ref": "#/components/schemas/UserFactoryResponseDTO"}, "type": "array", "title": "Managed Factories"}}, "type": "object", "required": ["active_factories", "pending_requests", "managed_factories"], "title": "MyFactoriesDTO", "description": "DTO for current user's factories."}, "OrderAmountUpdateDTO": {"properties": {"total_amount": {"type": "integer", "minimum": 0.0, "title": "Total Amount", "description": "新的总数量"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "变更备注"}}, "type": "object", "required": ["total_amount"], "title": "OrderAmountUpdateDTO", "description": "DTO for updating order total amount."}, "OrderBundleCreateDTO": {"properties": {"size": {"type": "string", "maxLength": 20, "minLength": 1, "title": "Size", "description": "尺码"}, "quantity": {"type": "integer", "minimum": 0.0, "title": "Quantity", "description": "扎件数"}, "color": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Color", "description": "颜色"}, "notes": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Notes", "description": "备注"}, "planned_start_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Planned Start Date", "description": "计划开始时间"}, "planned_end_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Planned End Date", "description": "计划完成时间"}, "cutting_machine": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Cutting Machine", "description": "裁剪机床"}, "sewing_machine": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Sewing Machine", "description": "缝制机床"}, "order_no": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Order No", "description": "订单号"}, "order_part_no": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Order Part No", "description": "订单部位号"}, "skc_no": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Skc No", "description": "款号"}, "bundle_sequence": {"type": "integer", "minimum": 1.0, "title": "Bundle Sequence", "description": "扎序号(同一部位、同一尺码内)"}, "cutter_user_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Cutter User Id", "description": "裁剪工ID"}, "sewer_user_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Sewer User Id", "description": "缝制工ID"}, "qc_user_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Qc User Id", "description": "质检员ID"}}, "type": "object", "required": ["size", "quantity", "color", "order_no", "order_part_no", "skc_no", "bundle_sequence"], "title": "OrderBundleCreateDTO", "description": "DTO for creating a new order bundle."}, "OrderBundleListDTO": {"properties": {"order_bundles": {"items": {"$ref": "#/components/schemas/OrderBundleResponseDTO"}, "type": "array", "title": "Order Bundles", "description": "订单扎列表"}, "total": {"type": "integer", "title": "Total", "description": "总数量"}}, "type": "object", "required": ["order_bundles", "total"], "title": "OrderBundleListDTO", "description": "DTO for order bundle list response."}, "OrderBundleOperationResultDTO": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "操作是否成功"}, "message": {"type": "string", "title": "Message", "description": "结果消息"}, "order_bundle_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Order Bundle Id", "description": "订单扎ID"}, "order_bundle_no": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Order Bundle No", "description": "订单扎号"}, "details": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Details", "description": "详细信息"}}, "type": "object", "required": ["success", "message"], "title": "OrderBundleOperationResultDTO", "description": "DTO for order bundle operation results."}, "OrderBundleProductionUpdateDTO": {"properties": {"completed_quantity": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Completed Quantity", "description": "已完成件数"}, "defective_quantity": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Defective Quantity", "description": "次品件数"}, "rework_quantity": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Rework Quantity", "description": "返工件数"}, "quality_level": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Quality Level", "description": "质量等级"}, "quality_notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Quality Notes", "description": "质量备注"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "生产备注"}}, "type": "object", "title": "OrderBundleProductionUpdateDTO", "description": "DTO for updating order bundle production."}, "OrderBundleResponseDTO": {"properties": {"size": {"type": "string", "maxLength": 20, "minLength": 1, "title": "Size", "description": "尺码"}, "quantity": {"type": "integer", "minimum": 0.0, "title": "Quantity", "description": "扎件数"}, "color": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Color", "description": "颜色"}, "notes": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Notes", "description": "备注"}, "planned_start_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Planned Start Date", "description": "计划开始时间"}, "planned_end_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Planned End Date", "description": "计划完成时间"}, "cutting_machine": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Cutting Machine", "description": "裁剪机床"}, "sewing_machine": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Sewing Machine", "description": "缝制机床"}, "id": {"type": "integer", "title": "Id", "description": "订单扎ID"}, "factory_id": {"type": "integer", "title": "Factory Id", "description": "工厂ID"}, "order_no": {"type": "string", "title": "Order No", "description": "订单号"}, "order_bundle_no": {"type": "string", "title": "Order Bundle No", "description": "订单扎号"}, "order_part_no": {"type": "string", "title": "Order Part No", "description": "订单部位号"}, "skc_no": {"type": "string", "title": "Skc No", "description": "款号"}, "bundle_sequence": {"type": "integer", "title": "Bundle Sequence", "description": "扎序号"}, "status": {"$ref": "#/components/schemas/BundleStatusEnum", "description": "扎状态"}, "completed_quantity": {"type": "integer", "title": "Completed Quantity", "description": "已完成件数"}, "defective_quantity": {"type": "integer", "title": "Defective Quantity", "description": "次品件数"}, "rework_quantity": {"type": "integer", "title": "Rework Quantity", "description": "返工件数"}, "progress_percentage": {"type": "integer", "title": "Progress Percentage", "description": "完成百分比"}, "actual_start_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Actual Start Date", "description": "实际开始时间"}, "actual_end_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Actual End Date", "description": "实际完成时间"}, "cut_start_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Cut Start Date", "description": "裁剪开始时间"}, "cut_end_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Cut End Date", "description": "裁剪完成时间"}, "sew_start_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Sew Start Date", "description": "缝制开始时间"}, "sew_end_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Sew End Date", "description": "缝制完成时间"}, "qc_start_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Qc Start Date", "description": "质检开始时间"}, "qc_end_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Qc End Date", "description": "质检完成时间"}, "cutter_user_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Cutter User Id", "description": "裁剪工ID"}, "sewer_user_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Sewer User Id", "description": "缝制工ID"}, "qc_user_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Qc User Id", "description": "质检员ID"}, "cutter_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Cutter Name", "description": "裁剪工姓名"}, "sewer_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Sewer Name", "description": "缝制工姓名"}, "qc_user_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Qc User Name", "description": "质检员姓名"}, "quality_level": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Quality Level", "description": "质量等级(A/B/C)"}, "quality_notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Quality Notes", "description": "质量备注"}, "good_quantity": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Good Quantity", "description": "良品数量"}, "processing_time": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Processing Time", "description": "总加工时长(分钟)"}, "cutting_time": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Cutting Time", "description": "裁剪时长(分钟)"}, "sewing_time": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Sewing Time", "description": "缝制时长(分钟)"}, "qc_time": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Qc Time", "description": "质检时长(分钟)"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "更新时间"}}, "type": "object", "required": ["size", "quantity", "color", "id", "factory_id", "order_no", "order_bundle_no", "order_part_no", "skc_no", "bundle_sequence", "status", "completed_quantity", "defective_quantity", "rework_quantity", "progress_percentage", "created_at", "updated_at"], "title": "OrderBundleResponseDTO", "description": "DTO for order bundle response."}, "OrderBundleStatisticsDTO": {"properties": {"total_order_bundles": {"type": "integer", "title": "Total Order Bundles", "description": "总订单扎数"}, "total_quantity": {"type": "integer", "title": "Total Quantity", "description": "总件数"}, "completed_quantity": {"type": "integer", "title": "Completed Quantity", "description": "已完成件数"}, "defective_quantity": {"type": "integer", "title": "Defective Quantity", "description": "次品件数"}, "rework_quantity": {"type": "integer", "title": "Rework Quantity", "description": "返工件数"}, "good_quantity": {"type": "integer", "title": "Good Quantity", "description": "良品件数"}, "completion_percentage": {"type": "number", "title": "Completion Percentage", "description": "完成百分比"}, "quality_rate": {"type": "number", "title": "Quality Rate", "description": "质量合格率"}, "status_breakdown": {"additionalProperties": true, "type": "object", "title": "Status Breakdown", "description": "状态分解"}, "planned_bundles": {"type": "integer", "title": "Planned Bundles", "description": "计划中扎数"}, "cutting_bundles": {"type": "integer", "title": "Cutting Bundles", "description": "裁剪中扎数"}, "cut_completed_bundles": {"type": "integer", "title": "Cut Completed Bundles", "description": "裁剪完成扎数"}, "sewing_bundles": {"type": "integer", "title": "Sewing <PERSON>", "description": "缝制中扎数"}, "sew_completed_bundles": {"type": "integer", "title": "Sew Completed Bundles", "description": "缝制完成扎数"}, "quality_check_bundles": {"type": "integer", "title": "Quality Check Bundles", "description": "质检中扎数"}, "completed_bundles": {"type": "integer", "title": "Completed Bundles", "description": "已完成扎数"}, "rework_bundles": {"type": "integer", "title": "Rework Bundles", "description": "返工扎数"}, "on_hold_bundles": {"type": "integer", "title": "On Hold Bundles", "description": "暂停扎数"}, "cancelled_bundles": {"type": "integer", "title": "Cancelled <PERSON><PERSON><PERSON>", "description": "已取消扎数"}}, "type": "object", "required": ["total_order_bundles", "total_quantity", "completed_quantity", "defective_quantity", "rework_quantity", "good_quantity", "completion_percentage", "quality_rate", "status_breakdown", "planned_bundles", "cutting_bundles", "cut_completed_bundles", "sewing_bundles", "sew_completed_bundles", "quality_check_bundles", "completed_bundles", "rework_bundles", "on_hold_bundles", "cancelled_bundles"], "title": "OrderBundleStatisticsDTO", "description": "DTO for order bundle statistics."}, "OrderBundleStatusUpdateDTO": {"properties": {"status": {"$ref": "#/components/schemas/BundleStatusEnum", "description": "新状态"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "状态变更备注"}, "user_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "User Id", "description": "操作用户ID"}, "machine": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Machine", "description": "机床编号"}}, "type": "object", "required": ["status"], "title": "OrderBundleStatusUpdateDTO", "description": "DTO for updating order bundle status."}, "OrderBundleUpdateDTO": {"properties": {"quantity": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Quantity", "description": "扎件数"}, "status": {"anyOf": [{"$ref": "#/components/schemas/BundleStatusEnum"}, {"type": "null"}], "description": "扎状态"}, "completed_quantity": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Completed Quantity", "description": "已完成件数"}, "defective_quantity": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Defective Quantity", "description": "次品件数"}, "rework_quantity": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Rework Quantity", "description": "返工件数"}, "notes": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Notes", "description": "备注"}, "planned_start_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Planned Start Date", "description": "计划开始时间"}, "planned_end_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Planned End Date", "description": "计划完成时间"}, "cutting_machine": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Cutting Machine", "description": "裁剪机床"}, "sewing_machine": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Sewing Machine", "description": "缝制机床"}, "cutter_user_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Cutter User Id", "description": "裁剪工ID"}, "sewer_user_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Sewer User Id", "description": "缝制工ID"}, "qc_user_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Qc User Id", "description": "质检员ID"}, "quality_level": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Quality Level", "description": "质量等级(A/B/C)"}, "quality_notes": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Quality Notes", "description": "质量备注"}}, "type": "object", "title": "OrderBundleUpdateDTO", "description": "DTO for updating an order bundle."}, "OrderCraftCreateDTO": {"properties": {"craft_code": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Craft Code", "description": "工艺代码"}, "craft_name": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Craft Name", "description": "工艺名称"}, "order": {"type": "integer", "minimum": 0.0, "title": "Order", "description": "工艺顺序"}, "is_required": {"type": "boolean", "title": "Is Required", "description": "是否必需", "default": true}, "estimated_duration_hours": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Estimated Duration Hours", "description": "预计耗时(小时)"}, "notes": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Notes", "description": "备注"}, "order_craft_routes": {"items": {"$ref": "#/components/schemas/OrderCraftRouteCreateDTO"}, "type": "array", "title": "Order Craft Routes", "description": "工艺路线列表"}}, "type": "object", "required": ["craft_code", "order"], "title": "OrderCraftCreateDTO", "description": "DTO for creating order crafts."}, "OrderCraftOperationResultDTO": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "操作是否成功"}, "message": {"type": "string", "title": "Message", "description": "结果消息"}, "order_craft_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Order Craft Id", "description": "订单工艺ID"}, "order_craft_route_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Order Craft Route Id", "description": "订单工艺路线ID"}, "details": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Details", "description": "详细信息"}}, "type": "object", "required": ["success", "message"], "title": "OrderCraftOperationResultDTO", "description": "DTO for order craft operation results."}, "OrderCraftProgressDTO": {"properties": {"craft_code": {"type": "string", "title": "Craft Code", "description": "工艺代码"}, "craft_route_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Craft Route Id", "description": "工艺路线ID"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "进度备注"}}, "type": "object", "required": ["craft_code"], "title": "OrderCraftProgressDTO", "description": "DTO for updating order craft progress."}, "OrderCraftResponseDTO": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID"}, "order_no": {"type": "string", "title": "Order No", "description": "订单号"}, "craft_code": {"type": "string", "title": "Craft Code", "description": "工艺代码"}, "craft_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Craft Name", "description": "工艺名称"}, "order": {"type": "integer", "title": "Order", "description": "工艺顺序"}, "is_required": {"type": "boolean", "title": "Is Required", "description": "是否必需"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "是否活跃"}, "status": {"type": "string", "title": "Status", "description": "状态"}, "started_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Started At", "description": "开始时间"}, "completed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completed At", "description": "完成时间"}, "estimated_duration_hours": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Estimated Duration Hours", "description": "预计耗时(小时)"}, "actual_duration_hours": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Actual Duration Hours", "description": "实际耗时(小时)"}, "completion_percentage": {"type": "number", "title": "Completion Percentage", "description": "完成百分比", "default": 0.0}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "备注"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "更新时间"}, "order_craft_routes": {"items": {"$ref": "#/components/schemas/OrderCraftRouteResponseDTO"}, "type": "array", "title": "Order Craft Routes", "description": "工艺路线列表"}}, "type": "object", "required": ["id", "order_no", "craft_code", "order", "is_required", "is_active", "status", "created_at", "updated_at"], "title": "OrderCraftResponseDTO", "description": "DTO for order craft response."}, "OrderCraftRouteCreateDTO": {"properties": {"skill_code": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Skill Code", "description": "技能代码"}, "name": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Name", "description": "路线名称"}, "code": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Code", "description": "路线代码"}, "order": {"type": "integer", "minimum": 0.0, "title": "Order", "description": "工序顺序"}, "measurement_types": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Measurement Types", "description": "测量类型列表"}, "registration_types": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Registration Types", "description": "登记类型列表"}, "is_required": {"type": "boolean", "title": "Is Required", "description": "是否必需", "default": true}, "estimated_duration_minutes": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Estimated Duration Minutes", "description": "预计耗时(分钟)"}, "assigned_user_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Assigned User Id", "description": "指定用户ID"}, "price": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "string"}, {"type": "null"}], "title": "Price", "description": "单价"}, "total_cost": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "string"}, {"type": "null"}], "title": "Total Cost", "description": "总成本"}, "notes": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Notes", "description": "备注"}}, "type": "object", "required": ["skill_code", "order"], "title": "OrderCraftRouteCreateDTO", "description": "DTO for creating order craft routes."}, "OrderCraftRouteResponseDTO": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID"}, "order_craft_id": {"type": "integer", "title": "Order Craft Id", "description": "订单工艺ID"}, "skill_code": {"type": "string", "title": "Skill Code", "description": "技能代码"}, "skill_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Skill Name", "description": "技能名称"}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name", "description": "路线名称"}, "code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Code", "description": "路线代码"}, "order": {"type": "integer", "title": "Order", "description": "工序顺序"}, "measurement_types": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Measurement Types", "description": "测量类型列表"}, "registration_types": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Registration Types", "description": "登记类型列表"}, "is_required": {"type": "boolean", "title": "Is Required", "description": "是否必需"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "是否活跃"}, "status": {"type": "string", "title": "Status", "description": "状态"}, "started_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Started At", "description": "开始时间"}, "completed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completed At", "description": "完成时间"}, "assigned_user_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Assigned User Id", "description": "指定用户ID"}, "assigned_user_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Assigned User Name", "description": "指定用户姓名"}, "estimated_duration_minutes": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Estimated Duration Minutes", "description": "预计耗时(分钟)"}, "actual_duration_minutes": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Actual Duration Minutes", "description": "实际耗时(分钟)"}, "price": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Price", "description": "单价"}, "total_cost": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Total Cost", "description": "总成本"}, "quality_score": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Quality Score", "description": "质量分数"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "备注"}, "completion_notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Completion Notes", "description": "完成备注"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "更新时间"}}, "type": "object", "required": ["id", "order_craft_id", "skill_code", "order", "is_required", "is_active", "status", "created_at", "updated_at"], "title": "OrderCraftRouteResponseDTO", "description": "DTO for order craft route response."}, "OrderCraftRouteStatusUpdateDTO": {"properties": {"status": {"type": "string", "title": "Status", "description": "新状态 (pending, in_progress, completed, skipped)"}, "quality_score": {"anyOf": [{"type": "integer", "maximum": 100.0, "minimum": 0.0}, {"type": "null"}], "title": "Quality Score", "description": "质量分数"}, "completion_notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Completion Notes", "description": "完成备注"}}, "type": "object", "required": ["status"], "title": "OrderCraftRouteStatusUpdateDTO", "description": "DTO for updating order craft route status."}, "OrderCraftStatisticsDTO": {"properties": {"total_order_crafts": {"type": "integer", "title": "Total Order Crafts", "description": "总订单工艺数"}, "pending_crafts": {"type": "integer", "title": "Pending Crafts", "description": "待处理工艺数"}, "in_progress_crafts": {"type": "integer", "title": "In Progress Crafts", "description": "进行中工艺数"}, "completed_crafts": {"type": "integer", "title": "Completed Crafts", "description": "已完成工艺数"}, "skipped_crafts": {"type": "integer", "title": "Skipped Crafts", "description": "跳过工艺数"}, "active_crafts": {"type": "integer", "title": "Active Crafts", "description": "活跃工艺数"}, "inactive_crafts": {"type": "integer", "title": "Inactive Crafts", "description": "非活跃工艺数"}, "status_breakdown": {"additionalProperties": true, "type": "object", "title": "Status Breakdown", "description": "状态分解"}, "active_breakdown": {"additionalProperties": true, "type": "object", "title": "Active Breakdown", "description": "活跃状态分解"}}, "type": "object", "required": ["total_order_crafts", "pending_crafts", "in_progress_crafts", "completed_crafts", "skipped_crafts", "active_crafts", "inactive_crafts", "status_breakdown", "active_breakdown"], "title": "OrderCraftStatisticsDTO", "description": "DTO for order craft statistics."}, "OrderCraftStatusUpdateDTO": {"properties": {"status": {"type": "string", "title": "Status", "description": "新状态 (pending, in_progress, completed, skipped)"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "状态变更备注"}}, "type": "object", "required": ["status"], "title": "OrderCraftStatusUpdateDTO", "description": "DTO for updating order craft status."}, "OrderCraftWorkflowDTO": {"properties": {"order_crafts": {"items": {"$ref": "#/components/schemas/OrderCraftCreateDTO"}, "type": "array", "minItems": 1, "title": "Order Crafts", "description": "订单工艺配置列表"}}, "type": "object", "required": ["order_crafts"], "title": "OrderCraftWorkflowDTO", "description": "DTO for order craft workflow configuration."}, "OrderCreateDTO": {"properties": {"skc_no": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Skc No", "description": "款号"}, "external_skc_no": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "External Skc No", "description": "外部款号"}, "order_no": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Order No", "description": "订单号"}, "external_order_no": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "External Order No", "description": "外部订单号"}, "external_order_no2": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "External Order No2", "description": "外部订单号2"}, "cost": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "string"}, {"type": "null"}], "title": "Cost", "description": "成本"}, "price": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "string"}, {"type": "null"}], "title": "Price", "description": "价格"}, "expect_finished_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expect Finished At", "description": "预期完成时间"}, "owner_user_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Owner User Id", "description": "负责人用户ID"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "订单描述"}, "notes": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Notes", "description": "备注"}, "order_lines": {"items": {"$ref": "#/components/schemas/OrderLineCreateDTO"}, "type": "array", "minItems": 1, "title": "Order Lines", "description": "订单行列表"}, "order_crafts": {"anyOf": [{"items": {"$ref": "#/components/schemas/OrderCraftCreateDTO"}, "type": "array"}, {"type": "null"}], "title": "Order Crafts", "description": "订单工艺配置列表"}}, "type": "object", "required": ["skc_no", "order_no", "order_lines"], "title": "OrderCreateDTO", "description": "DTO for creating a new order."}, "OrderDashboardDTO": {"properties": {"order_statistics": {"$ref": "#/components/schemas/OrderStatisticsDTO", "description": "订单统计"}, "recent_orders": {"items": {"$ref": "#/components/schemas/OrderSummaryDTO"}, "type": "array", "title": "Recent Orders", "description": "最近订单"}, "overdue_orders": {"items": {"$ref": "#/components/schemas/OrderSummaryDTO"}, "type": "array", "title": "Overdue Orders", "description": "逾期订单"}, "orders_by_craft": {"additionalProperties": true, "type": "object", "title": "Orders By Craft", "description": "按工艺分组的订单"}, "production_summary": {"$ref": "#/components/schemas/OrderLineStatisticsDTO", "description": "生产汇总"}}, "type": "object", "required": ["order_statistics", "recent_orders", "overdue_orders", "orders_by_craft", "production_summary"], "title": "OrderDashboardDTO", "description": "DTO for order dashboard data."}, "OrderDetailResponseDTO": {"properties": {"skc_no": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Skc No", "description": "款号"}, "external_skc_no": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "External Skc No", "description": "外部款号"}, "order_no": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Order No", "description": "订单号"}, "external_order_no": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "External Order No", "description": "外部订单号"}, "external_order_no2": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "External Order No2", "description": "外部订单号2"}, "cost": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Cost", "description": "成本"}, "price": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Price", "description": "价格"}, "expect_finished_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expect Finished At", "description": "预期完成时间"}, "owner_user_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Owner User Id", "description": "负责人用户ID"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "订单描述"}, "notes": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Notes", "description": "备注"}, "id": {"type": "integer", "title": "Id", "description": "订单ID"}, "total_amount": {"type": "integer", "title": "Total Amount", "description": "总数量"}, "status": {"type": "string", "title": "Status", "description": "订单状态"}, "current_craft": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Current Craft", "description": "当前工艺"}, "current_craft_route": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Current Craft Route", "description": "当前工艺路线"}, "completion_percentage": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Completion Percentage", "description": "完成百分比"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "创建时间"}, "started_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Started At", "description": "开始时间"}, "finished_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Finished At", "description": "完成时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "更新时间"}, "order_lines": {"items": {"$ref": "#/components/schemas/OrderLineResponseDTO"}, "type": "array", "title": "Order Lines", "description": "订单行列表"}, "order_crafts": {"anyOf": [{"items": {"$ref": "#/components/schemas/OrderCraftResponseDTO"}, "type": "array"}, {"type": "null"}], "title": "Order Crafts", "description": "订单工艺配置列表"}, "order_parts": {"anyOf": [{"items": {"$ref": "#/components/schemas/OrderPartResponseDTO"}, "type": "array"}, {"type": "null"}], "title": "Order Parts", "description": "订单部位列表"}}, "type": "object", "required": ["skc_no", "order_no", "id", "total_amount", "status", "created_at", "updated_at", "order_lines"], "title": "OrderDetailResponseDTO", "description": "DTO for detailed order response with lines, crafts, and parts."}, "OrderLineCreateDTO": {"properties": {"size": {"type": "string", "maxLength": 20, "minLength": 1, "title": "Size", "description": "尺码"}, "amount": {"type": "integer", "minimum": 0.0, "title": "Amount", "description": "数量"}, "notes": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Notes", "description": "备注"}}, "type": "object", "required": ["size", "amount"], "title": "OrderLineCreateDTO", "description": "DTO for creating a new order line."}, "OrderLineProductionUpdateDTO": {"properties": {"order_line_id": {"type": "integer", "title": "Order Line Id", "description": "订单行ID"}, "produced_amount": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Produced Amount", "description": "已生产数量"}, "completed_amount": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Completed Amount", "description": "已完成数量"}, "notes": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Notes", "description": "备注"}}, "type": "object", "required": ["order_line_id"], "title": "OrderLineProductionUpdateDTO", "description": "DTO for updating order line production."}, "OrderLineResponseDTO": {"properties": {"size": {"type": "string", "maxLength": 20, "minLength": 1, "title": "Size", "description": "尺码"}, "amount": {"type": "integer", "title": "Amount", "description": "数量"}, "notes": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Notes", "description": "备注"}, "id": {"type": "integer", "title": "Id", "description": "订单行ID"}, "order_no": {"type": "string", "title": "Order No", "description": "订单号"}, "order_line_no": {"type": "string", "title": "Order Line No", "description": "订单行号"}, "produced_amount": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Produced Amount", "description": "已生产数量"}, "completed_amount": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Completed Amount", "description": "已完成数量"}, "completion_percentage": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Completion Percentage", "description": "完成百分比"}, "production_percentage": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Production Percentage", "description": "生产百分比"}, "remaining_amount": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Remaining Amount", "description": "剩余数量"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "更新时间"}}, "type": "object", "required": ["size", "amount", "id", "order_no", "order_line_no", "created_at", "updated_at"], "title": "OrderLineResponseDTO", "description": "DTO for order line response."}, "OrderLineStatisticsDTO": {"properties": {"total_lines": {"type": "integer", "title": "Total Lines", "description": "总订单行数"}, "completed_lines": {"type": "integer", "title": "Completed Lines", "description": "已完成订单行数"}, "in_progress_lines": {"type": "integer", "title": "In Progress Lines", "description": "进行中订单行数"}, "pending_lines": {"type": "integer", "title": "Pending Lines", "description": "待处理订单行数"}, "total_amount": {"type": "integer", "title": "Total Amount", "description": "总数量"}, "total_produced": {"type": "integer", "title": "Total Produced", "description": "总生产数量"}, "total_completed": {"type": "integer", "title": "Total Completed", "description": "总完成数量"}, "completion_percentage": {"type": "number", "title": "Completion Percentage", "description": "完成百分比"}, "production_percentage": {"type": "number", "title": "Production Percentage", "description": "生产百分比"}}, "type": "object", "required": ["total_lines", "completed_lines", "in_progress_lines", "pending_lines", "total_amount", "total_produced", "total_completed", "completion_percentage", "production_percentage"], "title": "OrderLineStatisticsDTO", "description": "DTO for order line statistics."}, "OrderListDTO": {"properties": {"orders": {"items": {"$ref": "#/components/schemas/OrderResponseDTO"}, "type": "array", "title": "Orders", "description": "订单列表"}, "total": {"type": "integer", "title": "Total", "description": "总数量"}}, "type": "object", "required": ["orders", "total"], "title": "OrderListDTO", "description": "DTO for order list response."}, "OrderOperationResultDTO": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "操作是否成功"}, "message": {"type": "string", "title": "Message", "description": "结果消息"}, "order_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Order Id", "description": "订单ID"}, "order_no": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Order No", "description": "订单号"}, "details": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Details", "description": "详细信息"}}, "type": "object", "required": ["success", "message"], "title": "OrderOperationResultDTO", "description": "DTO for order operation results."}, "OrderPartCreateDTO": {"properties": {"part_name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Part Name", "description": "部位名称"}, "part_type": {"$ref": "#/components/schemas/PartTypeEnum", "description": "部位类型"}, "color": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Color", "description": "颜色"}, "total_quantity": {"type": "integer", "minimum": 0.0, "title": "Total Quantity", "description": "部位总件数"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "部位描述"}, "notes": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Notes", "description": "备注"}, "planned_start_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Planned Start Date", "description": "计划开始时间"}, "planned_end_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Planned End Date", "description": "计划完成时间"}, "machine_no": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Machine No", "description": "机床编号"}, "process_route": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Process Route", "description": "工序路线"}, "supervisor_user_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Supervisor User Id", "description": "负责人用户ID"}, "order_no": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Order No", "description": "订单号"}, "skc_no": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Skc No", "description": "款号"}, "part_sequence": {"type": "integer", "minimum": 1.0, "title": "Part Sequence", "description": "部位序号(同一订单内)"}, "order_bundles": {"anyOf": [{"items": {"$ref": "#/components/schemas/OrderBundleCreateDTO"}, "type": "array"}, {"type": "null"}], "title": "Order Bundles", "description": "订单扎配置列表"}}, "type": "object", "required": ["part_name", "part_type", "color", "total_quantity", "order_no", "skc_no", "part_sequence"], "title": "OrderPartCreateDTO", "description": "DTO for creating a new order part."}, "OrderPartListDTO": {"properties": {"order_parts": {"items": {"$ref": "#/components/schemas/OrderPartResponseDTO"}, "type": "array", "title": "Order Parts", "description": "订单部位列表"}, "total": {"type": "integer", "title": "Total", "description": "总数量"}}, "type": "object", "required": ["order_parts", "total"], "title": "OrderPartListDTO", "description": "DTO for order part list response."}, "OrderPartOperationResultDTO": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "操作是否成功"}, "message": {"type": "string", "title": "Message", "description": "结果消息"}, "order_part_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Order Part Id", "description": "订单部位ID"}, "order_part_no": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Order Part No", "description": "订单部位号"}, "details": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Details", "description": "详细信息"}}, "type": "object", "required": ["success", "message"], "title": "OrderPartOperationResultDTO", "description": "DTO for order part operation results."}, "OrderPartResponseDTO": {"properties": {"part_name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Part Name", "description": "部位名称"}, "part_type": {"$ref": "#/components/schemas/PartTypeEnum", "description": "部位类型"}, "color": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Color", "description": "颜色"}, "total_quantity": {"type": "integer", "minimum": 0.0, "title": "Total Quantity", "description": "部位总件数"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "部位描述"}, "notes": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Notes", "description": "备注"}, "planned_start_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Planned Start Date", "description": "计划开始时间"}, "planned_end_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Planned End Date", "description": "计划完成时间"}, "machine_no": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Machine No", "description": "机床编号"}, "process_route": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Process Route", "description": "工序路线"}, "supervisor_user_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Supervisor User Id", "description": "负责人用户ID"}, "id": {"type": "integer", "title": "Id", "description": "订单部位ID"}, "factory_id": {"type": "integer", "title": "Factory Id", "description": "工厂ID"}, "order_no": {"type": "string", "title": "Order No", "description": "订单号"}, "order_part_no": {"type": "string", "title": "Order Part No", "description": "订单部位号"}, "skc_no": {"type": "string", "title": "Skc No", "description": "款号"}, "part_sequence": {"type": "integer", "title": "Part Sequence", "description": "部位序号"}, "status": {"$ref": "#/components/schemas/PartStatusEnum", "description": "部位状态"}, "completed_quantity": {"type": "integer", "title": "Completed Quantity", "description": "已完成件数"}, "progress_percentage": {"type": "integer", "title": "Progress Percentage", "description": "完成百分比"}, "actual_start_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Actual Start Date", "description": "实际开始时间"}, "actual_end_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Actual End Date", "description": "实际完成时间"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "更新时间"}, "supervisor_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Supervisor Name", "description": "负责人姓名"}}, "type": "object", "required": ["part_name", "part_type", "color", "total_quantity", "id", "factory_id", "order_no", "order_part_no", "skc_no", "part_sequence", "status", "completed_quantity", "progress_percentage", "created_at", "updated_at"], "title": "OrderPartResponseDTO", "description": "DTO for order part response."}, "OrderPartStatisticsDTO": {"properties": {"total_order_parts": {"type": "integer", "title": "Total Order Parts", "description": "总订单部位数"}, "total_quantity": {"type": "integer", "title": "Total Quantity", "description": "总件数"}, "completed_quantity": {"type": "integer", "title": "Completed Quantity", "description": "已完成件数"}, "completion_percentage": {"type": "number", "title": "Completion Percentage", "description": "完成百分比"}, "status_breakdown": {"additionalProperties": true, "type": "object", "title": "Status Breakdown", "description": "状态分解"}, "planned_parts": {"type": "integer", "title": "Planned Parts", "description": "计划中部位数"}, "cutting_parts": {"type": "integer", "title": "Cutting Parts", "description": "裁剪中部位数"}, "sewing_parts": {"type": "integer", "title": "Sewing Parts", "description": "缝制中部位数"}, "quality_check_parts": {"type": "integer", "title": "Quality Check Parts", "description": "质检中部位数"}, "completed_parts": {"type": "integer", "title": "Completed Parts", "description": "已完成部位数"}, "on_hold_parts": {"type": "integer", "title": "On Hold Parts", "description": "暂停部位数"}, "cancelled_parts": {"type": "integer", "title": "Cancelled Parts", "description": "已取消部位数"}}, "type": "object", "required": ["total_order_parts", "total_quantity", "completed_quantity", "completion_percentage", "status_breakdown", "planned_parts", "cutting_parts", "sewing_parts", "quality_check_parts", "completed_parts", "on_hold_parts", "cancelled_parts"], "title": "OrderPartStatisticsDTO", "description": "DTO for order part statistics."}, "OrderPartStatusUpdateDTO": {"properties": {"status": {"$ref": "#/components/schemas/PartStatusEnum", "description": "新状态"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "状态变更备注"}}, "type": "object", "required": ["status"], "title": "OrderPartStatusUpdateDTO", "description": "DTO for updating order part status."}, "OrderPartUpdateDTO": {"properties": {"part_name": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 1}, {"type": "null"}], "title": "Part Name", "description": "部位名称"}, "color": {"anyOf": [{"type": "string", "maxLength": 50, "minLength": 1}, {"type": "null"}], "title": "Color", "description": "颜色"}, "total_quantity": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Total Quantity", "description": "部位总件数"}, "status": {"anyOf": [{"$ref": "#/components/schemas/PartStatusEnum"}, {"type": "null"}], "description": "部位状态"}, "completed_quantity": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Completed Quantity", "description": "已完成件数"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "部位描述"}, "notes": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Notes", "description": "备注"}, "planned_start_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Planned Start Date", "description": "计划开始时间"}, "planned_end_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Planned End Date", "description": "计划完成时间"}, "actual_start_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Actual Start Date", "description": "实际开始时间"}, "actual_end_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Actual End Date", "description": "实际完成时间"}, "machine_no": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Machine No", "description": "机床编号"}, "process_route": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Process Route", "description": "工序路线"}, "supervisor_user_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Supervisor User Id", "description": "负责人用户ID"}}, "type": "object", "title": "OrderPartUpdateDTO", "description": "DTO for updating an order part."}, "OrderPartWithBundlesDTO": {"properties": {"part_name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Part Name", "description": "部位名称"}, "part_type": {"$ref": "#/components/schemas/PartTypeEnum", "description": "部位类型"}, "color": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Color", "description": "颜色"}, "total_quantity": {"type": "integer", "minimum": 0.0, "title": "Total Quantity", "description": "部位总件数"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "部位描述"}, "notes": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Notes", "description": "备注"}, "planned_start_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Planned Start Date", "description": "计划开始时间"}, "planned_end_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Planned End Date", "description": "计划完成时间"}, "machine_no": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Machine No", "description": "机床编号"}, "process_route": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Process Route", "description": "工序路线"}, "supervisor_user_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Supervisor User Id", "description": "负责人用户ID"}, "id": {"type": "integer", "title": "Id", "description": "订单部位ID"}, "factory_id": {"type": "integer", "title": "Factory Id", "description": "工厂ID"}, "order_no": {"type": "string", "title": "Order No", "description": "订单号"}, "order_part_no": {"type": "string", "title": "Order Part No", "description": "订单部位号"}, "skc_no": {"type": "string", "title": "Skc No", "description": "款号"}, "part_sequence": {"type": "integer", "title": "Part Sequence", "description": "部位序号"}, "status": {"$ref": "#/components/schemas/PartStatusEnum", "description": "部位状态"}, "completed_quantity": {"type": "integer", "title": "Completed Quantity", "description": "已完成件数"}, "progress_percentage": {"type": "integer", "title": "Progress Percentage", "description": "完成百分比"}, "actual_start_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Actual Start Date", "description": "实际开始时间"}, "actual_end_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Actual End Date", "description": "实际完成时间"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "更新时间"}, "supervisor_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Supervisor Name", "description": "负责人姓名"}, "order_bundles": {"items": {"$ref": "#/components/schemas/OrderBundleResponseDTO"}, "type": "array", "title": "Order Bundles", "description": "订单扎列表"}}, "type": "object", "required": ["part_name", "part_type", "color", "total_quantity", "id", "factory_id", "order_no", "order_part_no", "skc_no", "part_sequence", "status", "completed_quantity", "progress_percentage", "created_at", "updated_at", "order_bundles"], "title": "OrderPartWithBundlesDTO", "description": "DTO for order part with its order bundles."}, "OrderProductionUpdateDTO": {"properties": {"order_line_updates": {"items": {"$ref": "#/components/schemas/OrderLineProductionUpdateDTO"}, "type": "array", "title": "Order Line Updates", "description": "订单行更新列表"}}, "type": "object", "required": ["order_line_updates"], "title": "OrderProductionUpdateDTO", "description": "DTO for updating order line production."}, "OrderResponseDTO": {"properties": {"skc_no": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Skc No", "description": "款号"}, "external_skc_no": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "External Skc No", "description": "外部款号"}, "order_no": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Order No", "description": "订单号"}, "external_order_no": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "External Order No", "description": "外部订单号"}, "external_order_no2": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "External Order No2", "description": "外部订单号2"}, "cost": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Cost", "description": "成本"}, "price": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Price", "description": "价格"}, "expect_finished_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expect Finished At", "description": "预期完成时间"}, "owner_user_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Owner User Id", "description": "负责人用户ID"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "订单描述"}, "notes": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Notes", "description": "备注"}, "id": {"type": "integer", "title": "Id", "description": "订单ID"}, "total_amount": {"type": "integer", "title": "Total Amount", "description": "总数量"}, "status": {"type": "string", "title": "Status", "description": "订单状态"}, "current_craft": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Current Craft", "description": "当前工艺"}, "current_craft_route": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Current Craft Route", "description": "当前工艺路线"}, "completion_percentage": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Completion Percentage", "description": "完成百分比"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "创建时间"}, "started_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Started At", "description": "开始时间"}, "finished_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Finished At", "description": "完成时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "更新时间"}}, "type": "object", "required": ["skc_no", "order_no", "id", "total_amount", "status", "created_at", "updated_at"], "title": "OrderResponseDTO", "description": "DTO for order response."}, "OrderStatisticsDTO": {"properties": {"total_orders": {"type": "integer", "title": "Total Orders", "description": "总订单数"}, "active_orders": {"type": "integer", "title": "Active Orders", "description": "活跃订单数"}, "completed_orders": {"type": "integer", "title": "Completed Orders", "description": "已完成订单数"}, "overdue_orders": {"type": "integer", "title": "Overdue Orders", "description": "逾期订单数"}, "status_breakdown": {"additionalProperties": true, "type": "object", "title": "Status Breakdown", "description": "状态分解"}, "total_amount": {"type": "integer", "title": "Total Amount", "description": "总数量"}, "total_completed_amount": {"type": "integer", "title": "Total Completed Amount", "description": "总完成数量"}, "completion_rate": {"type": "number", "title": "Completion Rate", "description": "完成率"}}, "type": "object", "required": ["total_orders", "active_orders", "completed_orders", "overdue_orders", "status_breakdown", "total_amount", "total_completed_amount", "completion_rate"], "title": "OrderStatisticsDTO", "description": "DTO for order statistics."}, "OrderStatusUpdateDTO": {"properties": {"status": {"type": "string", "title": "Status", "description": "新状态"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "状态变更备注"}}, "type": "object", "required": ["status"], "title": "OrderStatusUpdateDTO", "description": "DTO for updating order status."}, "OrderSummaryDTO": {"properties": {"id": {"type": "integer", "title": "Id", "description": "订单ID"}, "order_no": {"type": "string", "title": "Order No", "description": "订单号"}, "skc_no": {"type": "string", "title": "Skc No", "description": "款号"}, "status": {"type": "string", "title": "Status", "description": "订单状态"}, "total_amount": {"type": "integer", "title": "Total Amount", "description": "总数量"}, "completed_amount": {"type": "integer", "title": "Completed Amount", "description": "已完成数量"}, "completion_percentage": {"type": "number", "title": "Completion Percentage", "description": "完成百分比"}, "owner_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Owner Name", "description": "负责人姓名"}, "current_craft": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Current Craft", "description": "当前工艺"}, "expect_finished_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expect Finished At", "description": "预期完成时间"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "创建时间"}}, "type": "object", "required": ["id", "order_no", "skc_no", "status", "total_amount", "completed_amount", "completion_percentage", "created_at"], "title": "OrderSummaryDTO", "description": "DTO for order summary information."}, "OrderUpdateDTO": {"properties": {"skc_no": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 1}, {"type": "null"}], "title": "Skc No", "description": "款号"}, "external_skc_no": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "External Skc No", "description": "外部款号"}, "external_order_no": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "External Order No", "description": "外部订单号"}, "external_order_no2": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "External Order No2", "description": "外部订单号2"}, "cost": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "string"}, {"type": "null"}], "title": "Cost", "description": "成本"}, "price": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "string"}, {"type": "null"}], "title": "Price", "description": "价格"}, "expect_finished_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expect Finished At", "description": "预期完成时间"}, "owner_user_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Owner User Id", "description": "负责人用户ID"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "订单描述"}, "notes": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Notes", "description": "备注"}}, "type": "object", "title": "OrderUpdateDTO", "description": "DTO for updating an order."}, "PartStatusEnum": {"type": "string", "enum": ["planned", "cutting", "sewing", "quality_check", "completed", "on_hold", "cancelled"], "title": "PartStatusEnum", "description": "Part status enumeration for DTOs."}, "PartTypeEnum": {"type": "string", "enum": ["front_body", "back_body", "sleeve", "collar", "pocket", "cuff", "waistband", "leg", "zipper", "button_placket", "lining", "accessories", "other"], "title": "PartTypeEnum", "description": "Part type enumeration for DTOs."}, "PermissionListDTO": {"properties": {"permissions": {"items": {"$ref": "#/components/schemas/PermissionResponseDTO"}, "type": "array", "title": "Permissions", "description": "List of permissions"}, "total": {"type": "integer", "title": "Total", "description": "Total number of permissions"}}, "type": "object", "required": ["permissions", "total"], "title": "PermissionListDTO", "description": "DTO for permission list response."}, "PermissionResponseDTO": {"properties": {"code": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Code", "description": "Unique permission code"}, "name": {"type": "string", "maxLength": 200, "minLength": 1, "title": "Name", "description": "Permission name"}, "parent_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Parent Id", "description": "Parent permission ID for hierarchical structure"}, "id": {"type": "integer", "title": "Id", "description": "Permission ID"}}, "type": "object", "required": ["code", "name", "id"], "title": "PermissionResponseDTO", "description": "DTO for permission response."}, "PermissionTreeDTO": {"properties": {"id": {"type": "integer", "title": "Id", "description": "Permission ID"}, "code": {"type": "string", "title": "Code", "description": "Permission code"}, "name": {"type": "string", "title": "Name", "description": "Permission name"}, "parent_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Parent Id", "description": "Parent permission ID"}, "children": {"items": {"$ref": "#/components/schemas/PermissionTreeDTO"}, "type": "array", "title": "Children", "description": "Child permissions"}}, "type": "object", "required": ["id", "code", "name"], "title": "PermissionTreeDTO", "description": "DTO for hierarchical permission tree."}, "PhonePasswordLoginDTO": {"properties": {"phone": {"type": "string", "pattern": "^1[3-9]\\d{9}$", "title": "Phone", "description": "Chinese mobile number"}, "password": {"type": "string", "minLength": 1, "title": "Password"}, "image_code": {"type": "string", "maxLength": 6, "minLength": 4, "title": "Image Code"}, "session_id": {"type": "string", "title": "Session Id", "description": "Session ID for image validation code"}}, "type": "object", "required": ["phone", "password", "image_code", "session_id"], "title": "PhonePasswordLoginDTO", "description": "DTO for phone + password + image validation code login."}, "PhoneSmsLoginDTO": {"properties": {"phone": {"type": "string", "pattern": "^1[3-9]\\d{9}$", "title": "Phone", "description": "Chinese mobile number"}, "sms_code": {"type": "string", "maxLength": 8, "minLength": 4, "title": "Sms Code"}, "image_code": {"type": "string", "maxLength": 6, "minLength": 4, "title": "Image Code"}, "session_id": {"type": "string", "title": "Session Id", "description": "Session ID for image validation code"}}, "type": "object", "required": ["phone", "sms_code", "image_code", "session_id"], "title": "PhoneSmsLoginDTO", "description": "DTO for phone + SMS + image validation code login."}, "RegistrationSummaryDTO": {"properties": {"total_craft_routes": {"type": "integer", "title": "Total Craft Routes", "description": "总工艺路线数"}, "available_craft_routes": {"type": "integer", "title": "Available Craft Routes", "description": "可登记工艺路线数"}, "total_parts": {"type": "integer", "title": "Total Parts", "description": "总部位数"}, "available_parts": {"type": "integer", "title": "Available Parts", "description": "可登记部位数"}, "total_bundles": {"type": "integer", "title": "Total Bundles", "description": "总扎数"}, "available_bundles": {"type": "integer", "title": "Available Bundles", "description": "可登记扎数"}, "completion_rate": {"type": "number", "title": "Completion Rate", "description": "完成率 (0-1)"}}, "type": "object", "required": ["total_craft_routes", "available_craft_routes", "total_parts", "available_parts", "total_bundles", "available_bundles", "completion_rate"], "title": "RegistrationSummaryDTO", "description": "登记汇总信息"}, "RemoveSkillDTO": {"properties": {"user_factory_skill_id": {"type": "integer", "title": "User Factory Skill Id", "description": "User factory skill ID"}, "reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reason", "description": "Reason for removal"}}, "type": "object", "required": ["user_factory_skill_id"], "title": "RemoveSkillDTO", "description": "DTO for removing skill from user."}, "RemoveUserFromFactoryDTO": {"properties": {"user_id": {"type": "integer", "title": "User Id", "description": "User ID to remove"}, "reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reason", "description": "Reason for removal"}}, "type": "object", "required": ["user_id"], "title": "RemoveUserFromFactoryDTO", "description": "DTO for removing user from factory."}, "RoleCreateDTO": {"properties": {"name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Name", "description": "Unique role name"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "Role description"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Whether the role is active", "default": true}, "permission_ids": {"items": {"type": "integer"}, "type": "array", "title": "Permission Ids", "description": "List of permission IDs to assign"}}, "type": "object", "required": ["name"], "title": "RoleCreateDTO", "description": "DTO for creating a new role."}, "RoleListDTO": {"properties": {"roles": {"items": {"$ref": "#/components/schemas/RoleResponseDTO"}, "type": "array", "title": "Roles", "description": "List of roles"}, "total": {"type": "integer", "title": "Total", "description": "Total number of roles"}}, "type": "object", "required": ["roles", "total"], "title": "RoleListDTO", "description": "DTO for role list response."}, "RolePermissionAssignDTO": {"properties": {"permission_ids": {"items": {"type": "integer"}, "type": "array", "title": "Permission Ids", "description": "List of permission IDs to assign"}}, "type": "object", "required": ["permission_ids"], "title": "RolePermissionAssignDTO", "description": "DTO for assigning permissions to role."}, "RolePermissionRemoveDTO": {"properties": {"permission_ids": {"items": {"type": "integer"}, "type": "array", "title": "Permission Ids", "description": "List of permission IDs to remove"}}, "type": "object", "required": ["permission_ids"], "title": "RolePermissionRemoveDTO", "description": "DTO for removing permissions from role."}, "RoleResponseDTO": {"properties": {"name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Name", "description": "Unique role name"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "Role description"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Whether the role is active", "default": true}, "id": {"type": "integer", "title": "Id", "description": "Role ID"}, "permissions": {"items": {"$ref": "#/components/schemas/PermissionResponseDTO"}, "type": "array", "title": "Permissions", "description": "Assigned permissions"}}, "type": "object", "required": ["name", "id"], "title": "RoleResponseDTO", "description": "DTO for role response."}, "RoleSummaryDTO": {"properties": {"id": {"type": "integer", "title": "Id", "description": "Role ID"}, "name": {"type": "string", "title": "Name", "description": "Role name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Role description"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Whether the role is active"}, "permission_count": {"type": "integer", "title": "Permission Count", "description": "Number of permissions assigned"}}, "type": "object", "required": ["id", "name", "is_active", "permission_count"], "title": "RoleSummaryDTO", "description": "DTO for role summary without permissions."}, "RoleUpdateDTO": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 1}, {"type": "null"}], "title": "Name", "description": "Role name"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "Role description"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active", "description": "Whether the role is active"}}, "type": "object", "title": "RoleUpdateDTO", "description": "DTO for updating a role."}, "SendSmsCodeDTO": {"properties": {"phone": {"type": "string", "pattern": "^1[3-9]\\d{9}$", "title": "Phone", "description": "Chinese mobile number"}, "image_code": {"type": "string", "maxLength": 6, "minLength": 4, "title": "Image Code"}, "session_id": {"type": "string", "title": "Session Id", "description": "Session ID for image validation code"}}, "type": "object", "required": ["phone", "image_code", "session_id"], "title": "SendSmsCodeDTO", "description": "DTO for requesting SMS validation code."}, "SendSmsCodeResponseDTO": {"properties": {"success": {"type": "boolean", "title": "Success"}, "message": {"type": "string", "title": "Message"}, "expires_in_seconds": {"type": "integer", "title": "Expires In Seconds"}}, "type": "object", "required": ["success", "message", "expires_in_seconds"], "title": "SendSmsCodeResponseDTO", "description": "Response DTO for SMS code sending."}, "SessionFactoryContextDTO": {"properties": {"factory_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Factory Id"}, "factory_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Factory Name"}, "factory_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Factory Code"}, "department_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Department Id"}, "department_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Department Name"}, "role": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Role"}, "employee_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Employee Id"}, "position": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Position"}, "is_manager": {"type": "boolean", "title": "Is Manager", "default": false}}, "type": "object", "title": "SessionFactoryContextDTO", "description": "DTO for factory context in session."}, "SessionStatusDTO": {"properties": {"is_valid": {"type": "boolean", "title": "Is <PERSON>"}, "user_session": {"anyOf": [{"$ref": "#/components/schemas/UserSessionDTO"}, {"type": "null"}]}, "message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Message"}}, "type": "object", "required": ["is_valid"], "title": "SessionStatusDTO", "description": "DTO for session status response."}, "SettlementStatusDTO": {"type": "string", "enum": ["pending", "included", "settled", "disputed", "excluded"], "title": "SettlementStatusDTO", "description": "结算状态枚举"}, "SkillCreateDTO": {"properties": {"code": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Code", "description": "Unique skill code"}, "name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Name", "description": "Skill name"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "Skill description"}, "category": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Category", "description": "Skill category"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Whether the skill is active", "default": true}}, "type": "object", "required": ["code", "name"], "title": "SkillCreateDTO", "description": "DTO for creating a new skill."}, "SkillListDTO": {"properties": {"skills": {"items": {"$ref": "#/components/schemas/SkillResponseDTO"}, "type": "array", "title": "Skills", "description": "List of skills"}, "total": {"type": "integer", "title": "Total", "description": "Total number of skills"}}, "type": "object", "required": ["skills", "total"], "title": "SkillListDTO", "description": "DTO for skill list response."}, "SkillOperationResultDTO": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "Whether operation succeeded"}, "message": {"type": "string", "title": "Message", "description": "Result message"}, "skill_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Skill Id", "description": "Skill ID affected"}, "user_factory_skill_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "User Factory Skill Id", "description": "User factory skill ID affected"}, "details": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Details", "description": "Additional operation details"}}, "type": "object", "required": ["success", "message"], "title": "SkillOperationResultDTO", "description": "DTO for skill operation results."}, "SkillResponseDTO": {"properties": {"code": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Code", "description": "Unique skill code"}, "name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Name", "description": "Skill name"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "Skill description"}, "category": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Category", "description": "Skill category"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Whether the skill is active", "default": true}, "id": {"type": "integer", "title": "Id", "description": "Skill ID"}}, "type": "object", "required": ["code", "name", "id"], "title": "SkillResponseDTO", "description": "DTO for skill response."}, "SkillUpdateDTO": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 1}, {"type": "null"}], "title": "Name", "description": "Skill name"}, "description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "Skill description"}, "category": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Category", "description": "Skill category"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active", "description": "Whether the skill is active"}}, "type": "object", "title": "SkillUpdateDTO", "description": "DTO for updating a skill."}, "SuspendUserDTO": {"properties": {"user_id": {"type": "integer", "title": "User Id", "description": "User ID to suspend"}, "reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reason", "description": "Reason for suspension"}, "suspension_note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Suspension Note", "description": "Additional notes"}}, "type": "object", "required": ["user_id"], "title": "SuspendUserDTO", "description": "DTO for suspending user."}, "SwitchFactoryDTO": {"properties": {"factory_id": {"type": "integer", "title": "Factory Id"}}, "type": "object", "required": ["factory_id"], "title": "SwitchFactoryDTO", "description": "DTO for switching factory context."}, "UpdateSkillProficiencyDTO": {"properties": {"user_factory_skill_id": {"type": "integer", "title": "User Factory Skill Id", "description": "User factory skill ID"}, "proficiency_level": {"type": "string", "title": "Proficiency Level", "description": "New proficiency level"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "Updated notes"}}, "type": "object", "required": ["user_factory_skill_id", "proficiency_level"], "title": "UpdateSkillProficiencyDTO", "description": "DTO for updating skill proficiency."}, "UserCreateDTO": {"properties": {"username": {"type": "string", "maxLength": 50, "minLength": 3, "title": "Username"}, "email": {"type": "string", "format": "email", "title": "Email"}, "password": {"type": "string", "minLength": 8, "title": "Password"}, "full_name": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Full Name"}}, "type": "object", "required": ["username", "email", "password"], "title": "UserCreateDTO"}, "UserFactoryResponseDTO": {"properties": {"id": {"type": "integer", "title": "Id"}, "user_id": {"type": "integer", "title": "User Id"}, "factory_id": {"type": "integer", "title": "Factory Id"}, "department_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Department Id"}, "status": {"$ref": "#/components/schemas/UserFactoryStatusEnum"}, "role": {"$ref": "#/components/schemas/UserFactoryRoleEnum"}, "employee_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Employee Id"}, "position": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Position"}, "start_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Start Date"}, "end_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Date"}, "requested_at": {"type": "string", "format": "date-time", "title": "Requested At"}, "approved_by": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Approved By"}, "approved_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Approved At"}, "rejected_reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Rejected Reason"}, "factory_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Factory Name"}, "department_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Department Name"}, "user_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Name"}, "approver_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Approver Name"}}, "type": "object", "required": ["id", "user_id", "factory_id", "department_id", "status", "role", "employee_id", "position", "start_date", "end_date", "requested_at", "approved_by", "approved_at", "rejected_reason"], "title": "UserFactoryResponseDTO", "description": "Response DTO for UserFactory relationships."}, "UserFactoryRole": {"type": "string", "enum": ["worker", "supervisor", "manager", "admin"], "title": "UserFactoryRole", "description": "User's role in the factory."}, "UserFactoryRoleEnum": {"type": "string", "enum": ["worker", "supervisor", "manager", "admin"], "title": "UserFactoryRoleEnum", "description": "Role enum for API responses."}, "UserFactorySkillDTO": {"properties": {"id": {"type": "integer", "title": "Id", "description": "User factory skill ID"}, "skill": {"$ref": "#/components/schemas/SkillResponseDTO", "description": "Skill information"}, "proficiency_level": {"type": "string", "title": "Proficiency Level", "description": "Proficiency level (BEGINNER, INTERMEDIATE, ADVANCED, EXPERT)"}, "certified": {"type": "boolean", "title": "Certified", "description": "Whether user is certified"}, "certification_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Certification Date", "description": "Certification date (ISO format)"}, "certification_expires": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Certification Expires", "description": "Certification expiry date (ISO format)"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "Additional notes"}, "assigned_by": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Assigned By", "description": "ID of user who assigned/updated this skill"}, "assigned_at": {"type": "string", "title": "Assigned At", "description": "When skill was assigned (ISO format)"}, "updated_at": {"type": "string", "title": "Updated At", "description": "Last update time (ISO format)"}}, "type": "object", "required": ["id", "skill", "proficiency_level", "certified", "assigned_at", "updated_at"], "title": "UserFactorySkillDTO", "description": "DTO for user factory skill relationship."}, "UserFactoryStatusEnum": {"type": "string", "enum": ["pending", "approved", "rejected", "suspended", "resigned"], "title": "UserFactoryStatusEnum", "description": "Status enum for API responses."}, "UserOperationResultDTO": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "Whether operation succeeded"}, "message": {"type": "string", "title": "Message", "description": "Result message"}, "user_id": {"type": "integer", "title": "User Id", "description": "User ID affected"}, "details": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Details", "description": "Additional operation details"}}, "type": "object", "required": ["success", "message", "user_id"], "title": "UserOperationResultDTO", "description": "DTO for user operation results."}, "UserResponseDTO": {"properties": {"id": {"type": "integer", "title": "Id"}, "username": {"type": "string", "title": "Username"}, "email": {"type": "string", "title": "Email"}, "full_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Full Name"}, "is_active": {"type": "boolean", "title": "Is Active"}, "is_superuser": {"type": "boolean", "title": "Is Superuser"}, "avatar_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Avatar Url"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["id", "username", "email", "full_name", "is_active", "is_superuser", "avatar_url", "created_at", "updated_at"], "title": "UserResponseDTO"}, "UserSessionDTO": {"properties": {"user_id": {"type": "integer", "title": "User Id"}, "username": {"type": "string", "title": "Username"}, "session_id": {"type": "string", "title": "Session Id"}, "factory_context": {"$ref": "#/components/schemas/SessionFactoryContextDTO"}, "session_created_at": {"type": "string", "format": "date-time", "title": "Session Created At"}}, "type": "object", "required": ["user_id", "username", "session_id", "factory_context", "session_created_at"], "title": "UserSessionDTO", "description": "DTO for user session information."}, "UserSkillCreateDTO": {"properties": {"skill_id": {"type": "integer", "title": "Skill Id", "description": "Skill ID"}, "proficiency_level": {"type": "string", "title": "Proficiency Level", "description": "Proficiency level (BEGINNER, INTERMEDIATE, ADVANCED, EXPERT)", "default": "BEGINNER"}, "certified": {"type": "boolean", "title": "Certified", "description": "Whether user is initially certified", "default": false}, "certification_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Certification Date", "description": "Certification date (ISO format)"}, "certification_expires": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Certification Expires", "description": "Certification expiry date (ISO format)"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "Additional notes"}}, "type": "object", "required": ["skill_id"], "title": "UserSkillCreateDTO", "description": "DTO for creating user skill."}, "UserSkillsDTO": {"properties": {"user_factory_id": {"type": "integer", "title": "User Factory Id", "description": "User factory relationship ID"}, "skills": {"items": {"$ref": "#/components/schemas/UserFactorySkillDTO"}, "type": "array", "title": "Skills", "description": "List of user's skills"}, "total_skills": {"type": "integer", "title": "Total Skills", "description": "Total number of skills"}, "certified_skills": {"type": "integer", "title": "Certified Skills", "description": "Number of certified skills"}}, "type": "object", "required": ["user_factory_id", "skills", "total_skills", "certified_skills"], "title": "UserSkillsDTO", "description": "DTO for user's skills in a factory."}, "UserSummaryDTO": {"properties": {"id": {"type": "integer", "title": "Id", "description": "User ID"}, "username": {"type": "string", "title": "Username", "description": "Username"}, "full_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Full Name", "description": "User's full name"}, "email": {"type": "string", "title": "Email", "description": "Email address"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone", "description": "Phone number"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "Whether user is active"}, "role": {"anyOf": [{"$ref": "#/components/schemas/RoleSummaryDTO"}, {"type": "null"}], "description": "User's system role"}}, "type": "object", "required": ["id", "username", "email", "is_active"], "title": "UserSummaryDTO", "description": "DTO for user summary information."}, "UserUpdateDTO": {"properties": {"full_name": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Full Name"}, "avatar_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Avatar Url"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active"}}, "type": "object", "title": "UserUpdateDTO"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "src__application__dto__auth_dto__TokenResponseDTO": {"properties": {"access_token": {"type": "string", "title": "Access Token"}, "token_type": {"type": "string", "title": "Token Type", "default": "bearer"}, "session_id": {"type": "string", "title": "Session Id"}, "user_info": {"additionalProperties": true, "type": "object", "title": "User Info"}, "factory_context": {"additionalProperties": true, "type": "object", "title": "Factory Context"}}, "type": "object", "required": ["access_token", "session_id", "user_info", "factory_context"], "title": "TokenResponseDTO", "description": "Response DTO for authentication token."}, "src__application__dto__user_dto__TokenResponseDTO": {"properties": {"access_token": {"type": "string", "title": "Access Token"}, "token_type": {"type": "string", "title": "Token Type", "default": "bearer"}, "session_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Id"}, "factory_context": {"anyOf": [{"$ref": "#/components/schemas/FactoryContextDTO"}, {"type": "null"}]}}, "type": "object", "required": ["access_token"], "title": "TokenResponseDTO"}}, "securitySchemes": {"OAuth2PasswordBearer": {"type": "oauth2", "flows": {"password": {"scopes": {}, "tokenUrl": "/api/v1/auth/token"}}}}}}